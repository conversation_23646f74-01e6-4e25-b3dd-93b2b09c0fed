import { lazy } from 'react';

const Rated = lazy(() => import('@app/views/creativeRevenue/feeRating/rated'));
const FirstAdopt = lazy(() => import('@app/views/creativeRevenue/feeRating/firstAdopt'));
const SecondAdopt = lazy(() => import('@app/views/creativeRevenue/feeRating/secondAdopt'));
const Passed = lazy(() => import('@app/views/creativeRevenue/feeRating/passed'));
const RevenueQuery = lazy(() => import('@app/views/creativeRevenue/feeRating/revenueQuery'));
const AccountDetail = lazy(() => import('@app/views/creativeRevenue/feeRating/accountDetail'));
const WithdrawalApplicationMgr = lazy(() => import('@app/views/creativeRevenue/withdrawalApplication/withdrawalApplicationMgr'))

export default [
  {
    path: '/rated',
    component: Rated,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '待评级'],
      selectKeys: ['/view/rated'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/firstAdopt',
    component: FirstAdopt,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '一审稿费'],
      selectKeys: ['/view/firstAdopt'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/secondAdopt',
    component: SecondAdopt,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '二审稿费'],
      selectKeys: ['/view/secondAdopt'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/passed',  
    component: Passed,
    routeProps: {
      breadCrumb: ['创作收益管理', '稿费评级', '已通过'],
      selectKeys: ['/view/passed'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/revenueQuery',
    component: RevenueQuery,
    routeProps: {
      breadCrumb: ['创作收益管理', '收益查询'],
      selectKeys: ['/view/revenueQuery'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/feeRating/accountDetail/:accountId',
    component: AccountDetail,
    routeProps: {
      breadCrumb: ['创作收益管理', '收益查询', '账户详情'],
      selectKeys: ['/view/revenueQuery'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
  {
    path: '/withdrawalApplication',
    component: WithdrawalApplicationMgr,
    routeProps: {
      breadCrumb: ['创作收益管理', '提现申请'],
      selectKeys: ['/view/withdrawalApplication'],
      openKeys: ['/view/creativeRevenue'],
    },
    permission: '',
  },
];
