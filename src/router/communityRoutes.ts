import { lazy } from 'react';

const CommunityRecommend = lazy(() => import('@app/views/community/communityRecommend'));
const CircleMgr = lazy(() => import('@app/views/community/circleMgr'));
const CircleBlockMgr = lazy(() => import('@app/views/community/circleBlockMgr'));
const CircleContentMgr = lazy(() => import('@app/views/community/circleContentMgr'));
const CirclePictureMgr = lazy(() => import('@app/views/community/circlePictureMgr'));
const CircleOwnerMgr = lazy(() => import('@app/views/community/circleOwnerMgr'));
const CircleAuthorityMgr = lazy(() => import('@app/views/community/circleAuthorityMgr'));
const BoosterCardMgr = lazy(() => import('@app/views/community/boosterCardMgr'));
const BoosterContentMgr = lazy(() => import('@app/views/community/boosterContentMgr'));
const RecommendedUserOperations = lazy(
  () => import('@app/views/community/component/recommendedUserOperations')
);
const TopicRecommendation = lazy(
  () => import('@app/views/community/component/topicRecommendation')
);
const BannerManage = lazy(() => import('@app/views/community/component/bannerManage'));
const TmhList = lazy(() => import('@views/news/tmh'));
const MCNList = lazy(() => import('@views/news/chaokeList'));
const AssistantManager = lazy(() => import('@app/views/operates/assistantManager'));
const CircleHomePageMgr = lazy(() => import('@app/views/operates/circleHomePageMgr'));
const ActiveValueRule = lazy(() => import('@app/views/community/ActiveValueRule'));
const CircleActiveUserMgr = lazy(() => import('@app/views/operates/circleActiveUserMgr'));
const CircleCategoryMgr = lazy(() => import('@app/views/community/CircleCategoryMgr'));
const CircleClassifyDetail = lazy(() => import('@app/views/community/CircleClassifyDetail'));
const EarningsRule = lazy(() => import('@app/views/community/EarningsRule'));

export default [
  {
    path: '/circleCommunity',
    component: CircleHomePageMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '潮圈首页管理'],
      selectKeys: ['/view/circleCommunity'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/communityRecommend',
    component: CommunityRecommend,
    routeProps: {
      breadCrumb: ['用户内容管理', '用户内容推荐管理'],
      selectKeys: ['/view/communityRecommend'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleAIMgr',
    component: AssistantManager,
    routeProps: {
      breadCrumb: ['用户内容管理', '数字主理人'],
      selectKeys: ['/view/circleMgr'],
      openKeys: ['/view/communityPages'],
      category: 2,
    },
    permission: '',
  },
  {
    path: '/circleMgr',
    component: CircleMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '圈子管理'],
      selectKeys: ['/view/circleMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleAuthorityMgr',
    component: CircleAuthorityMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '用户内容审核权限'],
      selectKeys: ['/view/circleAuthorityMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/boosterCardMgr',
    component: BoosterCardMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '助推卡管理'],
      selectKeys: ['/view/boosterCardMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/boosterContentMgr',
    component: BoosterContentMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '助推内容列表'],
      selectKeys: ['/view/boosterContentMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleBlockMgr/:id/:name',
    component: CircleBlockMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '圈子管理'],
      selectKeys: ['/view/circleBlockMgr'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleContentMgr/:id/:name',
    component: CircleContentMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '圈子管理'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/activeValueRule',
    component: ActiveValueRule,
    routeProps: {
      breadCrumb: ['用户内容管理', '圈子管理', '圈友活跃值规则说明'],
      openKeys: ['/view/communityPages'],
      selectKeys: ['/view/circleMgr'],
    },
    permission: '',
  },
  {
    path: '/earningsRule',
    component: EarningsRule,
    routeProps: {
      breadCrumb: ['用户内容管理', '创作收益', '创作收益说明'],
      openKeys: ['/view/communityPages'],
      selectKeys: ['/view/feeRating'],
    },
    permission: '',
  },
  {
    path: '/circlePictureMgr/:id/:name',
    component: CirclePictureMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '圈子管理'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleOwnerMgr/:id/:name',
    component: CircleOwnerMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '主理人管理'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleCategoryMgr',
    component: CircleCategoryMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '圈子分类'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/circleClassifyDetail/:id/:name',
    component: CircleClassifyDetail,
    routeProps: {
      breadCrumb: ['运营管理', '圈子分类'],
      selectKeys: ['/view/columnManager'],
      openKeys: ['/view/operatePages'],
    },
    permission: '',
  },
  {
    path: '/circleActiveUser/:id/:name',
    component: CircleActiveUserMgr,
    routeProps: {
      breadCrumb: ['用户内容管理', '活跃圈友'],
      openKeys: ['/view/communityPages'],
      selectKeys: ['/view/circleMgr'],
    },
    permission: '',
  },
  {
    path: '/chaoke',
    component: MCNList,
    routeProps: {
      breadCrumb: ['用户内容管理', '潮客内容管理'],
      selectKeys: ['/view/chaoke'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/tmh',
    component: TmhList,
    routeProps: {
      breadCrumb: ['用户内容管理', '潮鸣号内容管理'],
      selectKeys: ['/view/tmh'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/recommendedUserOperations',
    component: RecommendedUserOperations,
    routeProps: {
      breadCrumb: ['用户内容管理', '用户内容推荐管理', '推荐用户运营位'],
      selectKeys: ['/view/recommendedUserOperations'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/bannerManage',
    component: BannerManage,
    routeProps: {
      breadCrumb: ['用户内容管理', '用户内容推荐管理', '轮播图管理'],
      selectKeys: ['/view/bannerManage'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
  {
    path: '/topicRecommendation',
    component: TopicRecommendation,
    routeProps: {
      breadCrumb: ['用户内容管理', '用户内容推荐管理', '话题推荐位'],
      selectKeys: ['/view/topicRecommendation'],
      openKeys: ['/view/communityPages'],
    },
    permission: '',
  },
];
