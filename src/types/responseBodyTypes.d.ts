import { CommonObject } from './commonTypes';
import { Session } from './systemTypes';

export interface CommonResponse<T = {}> {
  data: T;
  requestId?: string;
}

export interface ICommonTableList<T> {
  current: number;
  size: number;
  total: number;
  pages: number;
  records: T[];
}

export type IResLoginData = Session;
export type IResLoginRes = CommonResponse<IResLoginData>;

export interface IResNoticeViewData {
  content: string;
  status: string;
  tel: string;
  title: string;
}
export type IResNoticeViewRes = CommonResponse<IResNoticeViewData>;

export interface IResUploadData {
  url: string;
}

export type IResUploadRes = CommonResponse<IResUploadData>;

export interface ISysChannel {
  category_id: number;
  focus_carousel: boolean;
  focus_pic_mode: boolean;
  id: string;
  mode: number;
  name: string;
  tou_tiao?: boolean;
  focus_position: number;
  code: string
}

export interface IResReleaseListRecordsData {
  author: string;
  channel_id: string;
  channel_name: string;
  content_id: number;
  created_at: number;
  creator: string;
  doc_type: number;
  fixed_number: number;
  id: number;
  list_title: string;
  metadata_id: number;
  original_id: number;
  published_at: number;
  seq: number;
  source_channel_id: string;
  status: number;
  top_pushed: number;
  updated_at: number;
  visible: boolean;
  related_article_id: string;
  recommend_position: number;
}

export interface IResReleaseListData extends ICommonTableList<IResReleaseListRecordsData> {
  fixed_count: number;
}

export interface IResReleaseListAllData {
  channel: ISysChannel;
  release_list: IResReleaseListData;
}

export interface IResFocusListRecordsData {
  channel_article_id: number;
  creator: string;
  displayed: boolean;
  doc_type: number;
  fake_count: number;
  id: number;
  metadata_id: number;
  original_id: number;
  published_at: number;
  title: string;
  top_pushed: number;
}

export interface IResFocusListAllData {
  channel: ISysChannel;
  focus_list: ICommonTableList<IResFocusListRecordsData>;
  position_id: number;
  position: number;
}

export interface IResChannelRecommendRecordsData {
  id: number;
  pic_url: string;
  status: number;
  title: string;
  type: number;
  url: string;
}

export interface IResChannelRecommendAllData {
  channel: ISysChannel;
  recommend_list: ICommonTableList<IResChannelRecommendRecordsData>;
  on_show_count: number;
}

export type IOperationActionData = {
  action: string;
  opinion: string;
  time: string;
  user: string;
};

export type IOperationLogRecordData = {
  actions: IOperationActionData[];
  date: string;
};

export type IOperationLogRes = {
  logs: IOperationLogRecordData[];
};

export type TChannelRecommendArticle = {
  id: number;
  doc_type: number;
  doc_type_name: string;
  list_title: string;
};

export type TChannelRecommendArticleListRes = {
  article_list: TChannelRecommendArticle[];
};

export type TChannelRecommendDetail = {
  id: number;
  pic_url: string;
  status: number;
  title: string;
  type: number;
  url: string;
};

export type TChannelRecommendDetailRes = {
  recommend: TChannelRecommendDetail;
};

export interface IResTopNewsListRecordsData {
  channel_article_id: number;
  creator: string;
  displayed: boolean;
  doc_type: number;
  fake_count: number;
  id: number;
  metadata_id: number;
  original_id: number;
  published_at: number;
  title: string;
  top_pushed: number;
}

export interface IResTopNewsListAllData {
  article_list: IResTopNewsListRecordsData[];
}
