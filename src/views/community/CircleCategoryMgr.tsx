import {
  Col,
  Divider,
  Input,
  Modal,
  Row,
  message,
  InputNumber,
  Button,
  Radio,
  Select,
  Icon,
} from 'antd';
import { getCrumb, objectToQueryString, searchToObject, UserDetail } from '@app/utils/utils';
import { useHistory } from 'react-router';
import { useSelector, useDispatch, useStore } from 'react-redux';
import { clearTableList, getTableList } from '@app/action/tableList';
import { Table, OrderColumn } from '@components/common';
import React, { useEffect, useState } from 'react';
import { communityApi, userApi } from '@app/api';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import uuid from 'uuid';
import useXHR from '@app/utils/useXhr';
import EditCircleClassifyModal from './component/editCircleClassifyModal';

export default function ColumnClassifyMgr(props: any) {
  const history = useHistory();
  const dispatch = useDispatch();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
  const { session } = useStore().getState();
  const [type, setType] = useState(parseInt(searchToObject().type ?? 0));
  const [filter, setFilter] = useState({
    type: 0,
  });
  const { run, loading } = useXHR();
  // 1-智能标题、2-智能封面、3-智能配文
  // 100-潮奔奔，101-24小时会客厅  102-圈子数字代理人  103-公文搜索
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  const [editModal, setEditModal] = useState<any>({
    visible: false,
    key: uuid(),
    record: null,
  });

  const [userDetailModal, setUserDetailModal] = useState<any>({
    visible: false,
    key: uuid(),
    record: null,
  });

  const showUserDetailModal = (record: any, visible: boolean) => {
    run(userApi.getUserDetail, { accountId: record.account_id }, true).then((r: any) => {
      setUserDetailModal({
        key: Date.now(),
        visible: true,
        detail: r.data.account,
      });
    });
  };

  const listSort = (record: any, current: number, offset: 1 | -1 = 1) => {
    let data: any = {
      id: record.id,
      current,
      offset,
    };
    run(communityApi.sortCircleClass, data, true).then((res: any) => {
      message.success('操作成功');
      getData();
    });
  };

  const deleteClass = (record: any) => {
    Modal.confirm({
      title: '确定要删除吗？',
      onOk: () => {
        run(communityApi.deleteCircleClass, { id: record.id }, true).then((res: any) => {
          message.success('操作成功');
          getData();
        });
      },
    });
  };

  const changeOrder = (record: any, i: any) => {
    let position = i;
    const WAIT_TIME = 1000;
    const positionChange = (v: any) => {
      position = v;
    };

    Modal.confirm({
      title: <p>排序:{record.name}</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>排序值：</span>
          <InputNumber min={1} max={total} defaultValue={position} onChange={positionChange} />
        </div>
      ),
      onOk: (closeFunc: Function) => {
        if (!position) {
          message.error('请填写排序值');
          return;
        }

        dispatch(setConfig({ loading: true }));
        let data: any = {
          id: record.id,
          type: 0,
          position,
        };

        communityApi
          .moveCircleClass(data)
          .then((res: any) => {
            message.success('操作成功');
            getData();
            dispatch(setConfig({ loading: false }));
            closeFunc();
          })
          .catch(() => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  const columns: any = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="circle_class:sort"
          start={1}
          pos={getSeq(i)}
          end={total}
          onUp={() => listSort(record, getSeq(i), -1)}
          onDown={() => listSort(record, getSeq(i), 1)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'id',
      render: (a: any, b: any, index: number) => <span>{getSeq(index)}</span>,
      width: 50,
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      width: 80,
      render: (text: string, record: any) => (
        <a
          onClick={() => {
            dispatch(clearTableList());
            history.push(`/view/circleClassifyDetail/${record.id}/${text}`);
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '操作人',
      dataIndex: 'updated_by',
      width: 160,
    },
    {
      title: '操作时间',
      key: 'updated_at',
      dataIndex: 'updated_at',
      width: 160,
      render: (text: number, record: any) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'op',
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA
            perm="circle_class:update"
            onClick={() => setEditModal({ visible: true, key: uuid(), record })}
          >
            编辑
          </PermA>
          <Divider type="vertical" />
          <PermA perm="circle_class:sort" onClick={() => changeOrder(record, getSeq(i))}>
            排序
          </PermA>
          <Divider type="vertical" />
          <PermA perm="circle_class:delete" onClick={() => deleteClass(record)}>
            删除
          </PermA>
        </span>
      ),
      width: 180,
    },
  ];

  // 获取table
  const getData = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(getTableList('getCircleClassPGList', 'list', { current: cur, size, ...newFilter }));
  };

  const addRecord = () => {
    setEditModal({
      visible: true,
      key: uuid(),
      record: null,
    });
  };

  useEffect(() => {
    const { selectKeys, openKeys } = props;
    dispatch(setConfig({ selectKeys, openKeys }));
  }, []);

  useEffect(() => {
    getData(true);
  }, [filter]);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button
            onClick={() => {
              dispatch(clearTableList());
              history.goBack();
            }}
          >
            <Icon type="left-circle" />
            返回圈子管理
          </Button>

          <PermButton
            perm="circle_class:create"
            onClick={() => addRecord()}
            style={{
              marginLeft: 10,
            }}
          >
            添加分类
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Table
          func="getCircleClassPGList"
          index="list"
          pagination={true}
          rowKey="id"
          columns={columns}
          filter={filter}
        />

        <EditCircleClassifyModal
          {...editModal}
          onCancel={() => setEditModal({ visible: false, key: uuid(), record: null })}
          onOk={() => {
            getData();
            setEditModal({ visible: false, key: uuid(), record: null });
          }}
        />
      </div>
    </>
  );
}
