import SearchAndInput from '@components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { communityApi, serviceApi, userApi } from '@app/api';
import '@components/business/styles/business.scss';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { A, Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import _, { set } from 'lodash';
import PermsCheckbox from '@app/components/common/PermsCheckbox';
import { render } from 'react-dom';
import { getImageRatio } from '@app/utils/utils';
import TMFormList, { FormListTitle, TMFormListRef } from '@app/components/common/TMFormList';

const ServiceRecommendDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const [accountOptions, setAccountOptions] = useState([]);
  const [recommendLeft, setRecommendLeft] = useState<any[]>([]);
  const [recommendRight, setRecommendRight] = useState<any[]>([]);
  const { getFieldDecorator, getFieldValue, getFieldsValue, setFieldsValue } = props.form;

  useEffect(() => {
    if (props.visible) {
      setRecommendLeft([
        {
          url: props.record?.recommend_left[0]?.url ?? '',
          logo: props.record?.recommend_left[0]?.logo ?? '',
        },
        {
          url: props.record?.recommend_left[1]?.url ?? '',
          logo: props.record?.recommend_left[1]?.logo ?? '',
        },
      ]);
      setRecommendRight([
        {
          url: props.record?.recommend_right[0]?.url ?? '',
          name: props.record?.recommend_right[0]?.name ?? '',
        },
        {
          url: props.record?.recommend_right[1]?.url ?? '',
          name: props.record?.recommend_right[1]?.name ?? '',
        },
        {
          url: props.record?.recommend_right[2]?.url ?? '',
          name: props.record?.recommend_right[2]?.name ?? '',
        },
      ]);
    }
  }, [props.visible]);

  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body = {
          left_name: values.left_name,
          right_bg: values.right_bg,
          right_url: values.right_url,
          enabled: values.enabled,
          recommend_left: values.recommend_left,
          recommend_right: values.recommend_right,
        };

        dispatch(setConfig({ mLoading: true }));
        serviceApi
          .saveServiceRecommend(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onOk();
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const handleAccountSearch = _.debounce((val: any) => {
    if (!val?.trim()) {
      setAccountOptions([]);
      return;
    }

    communityApi
      .recommendAccount_Search({ keyword: val })
      .then(({ data }) => {
        const { list } = data as any;
        setAccountOptions(list || []);
      })
      .catch(() => {});
  }, 500);

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  return (
    <Drawer
      title={'2+1推荐位'}
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="保存"
      okPerm="web_link:recommend_save"
    >
      <Form {...formLayout}>
        <h3>
          2+1样式
          <Tooltip title={<img src={`/assets/sevice_header_3.png`} width={230} height={145} />}>
            <Icon type="question-circle" />
          </Tooltip>
        </h3>
        <Form.Item label="展示">
          {getFieldDecorator(`enabled`, {
            initialValue: props.record?.enabled ?? true,
            valuePropName: 'checked',
          })(<Switch />)}
        </Form.Item>

        <Form.Item label="模块标题">
          {getFieldDecorator(`left_name`, {
            initialValue: props.record?.left_name ?? '',
            rules: [{ max: 10, message: '最多可输入10字' }],
          })(<Input placeholder="最多可输入10字" />)}
        </Form.Item>

        <h3>左边区域</h3>
        {recommendLeft.map((item, index) => {
          return (
            <div key={`left_${index}`}>
              <Form.Item
                label={`图片${index + 1}`}
                extra="支持.jpg .jpeg .png图片格式，比例为167:90"
              >
                {getFieldDecorator(`recommend_left[${index}].logo`, {
                  initialValue: item.logo,
                  rules: [
                    {
                      required: getFieldValue('enabled'),
                      message: '请上传图片',
                    },
                  ],
                })(
                  <ImageUploader
                    ratio={167 / 90}
                    accept={['image/jpeg', 'image/png', 'image/jpg']}
                  />
                )}
              </Form.Item>
              <Form.Item label={`跳转链接${index + 1}`}>
                {getFieldDecorator(`recommend_left[${index}].url`, {
                  initialValue: item.url,
                  rules: [
                    {
                      required: getFieldValue('enabled'),
                      message: '请输入跳转链接',
                    },
                    {
                      validator: (rule: any, value: any, callback: any) => {
                        const regex = /^https?:\/\//;
                        if (!value) {
                          return callback();
                        } else if (!regex.test(value)) {
                          return callback('请正确填写链接格式');
                        } else {
                          return callback();
                        }
                      },
                    },
                  ],
                })(<Input placeholder="请输入跳转链接"></Input>)}
              </Form.Item>
            </div>
          );
        })}

        <h3>右边区域</h3>
        <Form.Item label="背景图" extra="支持.jpg .jpeg .png图片格式，比例为167:188">
          {getFieldDecorator(`right_bg`, {
            initialValue: props.record?.right_bg ?? '',
            rules: [
              {
                required: getFieldValue('enabled'),
                message: '请上传图片',
              },
            ],
          })(<ImageUploader ratio={167 / 188} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
        </Form.Item>

        <Form.Item label="跳转链接">
          {getFieldDecorator(`right_url`, {
            initialValue: props.record?.right_url ?? '',
            rules: [
              {
                validator: (rule: any, value: any, callback: any) => {
                  const regex = /^https?:\/\//;
                  if (!value) {
                    return callback();
                  } else if (!regex.test(value)) {
                    return callback('请正确填写链接格式');
                  } else {
                    return callback();
                  }
                },
              },
            ],
          })(<Input placeholder="请输入跳转链接"></Input>)}
        </Form.Item>
        {recommendRight.map((item, index) => {
          return (
            <div key={`right_${index}`}>
              <Form.Item label={`标题${index + 1}`}>
                {getFieldDecorator(`recommend_right[${index}].name`, {
                  initialValue: item.name,
                  rules: [
                    { required: getFieldValue('enabled'), message: '请输入标题', whitespace: true },
                    { max: 10, message: '最多可输入10字' },
                  ],
                })(<Input placeholder="最多可输入10字" />)}
              </Form.Item>

              <Form.Item label={`跳转链接${index + 1}`}>
                {getFieldDecorator(`recommend_right[${index}].url`, {
                  initialValue: item.url,
                  rules: [
                    {
                      required: getFieldValue('enabled'),
                      message: '请输入跳转链接',
                    },
                    {
                      validator: (rule: any, value: any, callback: any) => {
                        const regex = /^https?:\/\//;
                        if (!value) {
                          return callback();
                        } else if (!regex.test(value)) {
                          return callback('请正确填写链接格式');
                        } else {
                          return callback();
                        }
                      },
                    },
                  ],
                })(<Input placeholder="请输入跳转链接"></Input>)}
              </Form.Item>
            </div>
          );
        })}
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'ServiceRecommendDrawer' })(
  forwardRef<any, any>(ServiceRecommendDrawer)
);
