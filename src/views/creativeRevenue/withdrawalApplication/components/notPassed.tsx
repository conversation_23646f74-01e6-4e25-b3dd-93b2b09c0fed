import React, { useState, useEffect } from 'react';
import { Dropdown, Icon, Menu, Modal, message, Button, Col, Form, Input, Row, Select, Tooltip, Timeline, Table as NTable } from 'antd';
import { getCrumb, UserDetail, requirePerm, requirePerm4Function } from '@utils/utils';
import { CommonObject } from '@app/types';
import { useStore, useDispatch, useSelector } from 'react-redux';
import { getTableList, setTableList } from '@app/action/tableList';
import Table from './withdrawTable';
import SortableColumn from '@components/common/sortableColumn';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';
import moment from 'moment';
import { userApi, communityApi, creativeRevenueApi, releaseListApi } from '@app/api';
import { setConfig } from '@app/action/config';
import { Drawer, PreviewMCN } from '@app/components/common';
import { debounce } from 'lodash';
import { PermA, PermButton } from '@app/components/permItems';


interface FilterState {
    author_type: number | string;
    search_type: number;
    keyword: string;
    account_id: string;
    payout_status: number | string;
    sort_by?: number;
    sort_asc?: number;
}

interface NotPassedProps {
    data?: object;
}

const NotPassed: React.FC<NotPassedProps> = ({
    data
}) => {
    const match = useRouteMatch();
    const [loading, setLoading] = useState(false);
    const [filter, setFilter] = useState<FilterState>({
        author_type: '',
        search_type: 1,
        payout_status: '',
        keyword: '',
        account_id: '',
    });
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const [search, setSearch] = useState<{
        keyword: string;
        account_id?: string;
    }>({
        keyword: '',
    });
    const [userDetailModal, setUserDetailModal] = useState({
        visible: false,
        key: Date.now(),
        detail: null,
    });
    const [operateLog, setOperateLog] = useState<{
        visible: boolean;
        logs: any[];
    }>({
        visible: false,
        logs: [],
    });
    const [userRewardList, setUserRewardList] = useState<{
        visible: boolean;
        list: any[];
        amount: number;
        record: any;
    }>({
        visible: false,
        list: [],
        amount: 0,
        record: null,
    });
    const [authorSuggestions, setAuthorSuggestions] = useState<any[]>([]);
    const { session } = useStore().getState()
    const dispatch = useDispatch()
    const tableList = useSelector((state: any) => state.tableList);
    const tableCache = useSelector((state: any) => state.tableCache);
    const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);
    const [isInitialized, setIsInitialized] = useState(false);
    const [withdrawalModalVisible, setWithdrawalModalVisible] = useState(false);
    const [currentWithdrawal, setCurrentWithdrawal] = useState<any>(null);

    const [preview, setPreview] = useState({
        visible: false,
        skey: Date.now(),
        data: {},
    });

    useEffect(() => {
        if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
            getData({ current: tableCache.current, size: tableCache.size });
        } else {
            getData({ current: 1 });
        }

        setIsInitialized(true);
    }, []);

    const getData = (overlap: CommonObject = {}) => {
        const params = { ...getFilter(), ...overlap };
        dispatch(getTableList('withdrawNotPassedList', 'list', params));
    };

    const getFilter = () => {
        const { current, size } = tableList;
        const filters: CommonObject = { current, size };

        Object.keys(filter).forEach((key) => {
            const value = filter[key as keyof typeof filter];
            if (value !== '' && value !== undefined) {
                filters[key] = value;
            }
        });

        delete(filters['search_type'])

        return filters;
    }

    const showReason = (record: any) => {
        Modal.info({
            title: '不通过原因',
            content: record.fail_reason,
            okText: '关闭',
            okType: 'default',
        })
    }

    const handleFilterChange = (key: string, value: any) => {
        if (key === 'search_type') {
            setFilter({
                ...filter,
                [key]: value,
                keyword: '',
            });
            setSearch({
                keyword: '',
                account_id: undefined,
            });
        } else {
            setFilter({
                ...filter,
                [key]: value,
            });
        }
    };

    const handleKey = (e: { which: number }) => {
        if (e.which === 13) {
            handleSearch();
        }
    };

    const handleSearch = () => {
        // if (filter.search_type === 3) {
        //     setFilter({
        //         ...filter,
        //         account_id: search.account_id || '',
        //         keyword: '',
        //     });
        // } else {
        //     setFilter({
        //         ...filter,
        //         keyword: search.keyword,
        //         account_id: '',
        //     });
        // }
        setFilter({
            ...filter,
            account_id: search.account_id || '',
            keyword: '',
        });
    }

    const handleAuthorSearch = debounce((value: string) => {
        if (value) {
            communityApi
                .recommendAccount_Search({ keyword: value })
                .then((res: any) => {
                    setAuthorSuggestions(res.data?.list || []);
                })
                .catch(() => {
                    setAuthorSuggestions([]);
                });
        } else {
            setAuthorSuggestions([]);
        }
    }, 300);

    const handleSelectChange = (selectedKeys: any[]) => {
        setSelectedRowKeys(selectedKeys);
    }

    const showUserRewardList = (record: any) => {
        dispatch(setConfig({ loading: true }));
        creativeRevenueApi.withdrawDetail({
            request_id: record.id
        })
        .then((r: any) => {
            dispatch(setConfig({ loading: false }));
            const data = r.data?.list;
            let allAmount = 0
            data.forEach((item: any) => {
                allAmount += item.amount
            })
            if (data) {
                setUserRewardList({
                    visible: true,
                    list: data,
                    amount: allAmount,
                    record
                })
            }
        })
        .catch(() => {
            dispatch(setConfig({ loading: false }));
        });
        
    }

    const handleUserRewardListClose = () => {
        setUserRewardList({
            visible: false,
            list: [],
            amount: 0,
            record: null,
        })
    }

    const handleShowArticle = (record: any) => {
        if (record.fee_type === 1 && record.mlf_id) {
            toMlf(record.mlf_id)
        }
        else {
            setPreview({ visible: true, skey: Date.now(), data: { ...record, doc_type: 10, id: record.article_id } })
        }
    }

    const getOperateLog = (record: any) => {
        dispatch(setConfig({ loading: true }));
        creativeRevenueApi.withdrawRequestList({
            id: record.id
        })
        .then((r: any) => {
            dispatch(setConfig({ loading: false }));
            const data = r.data?.list;
            if (data) {

                setOperateLog({
                    visible: true,
                    logs: data,
                });
            }
        })
        .catch(() => {
            dispatch(setConfig({ loading: false }));
        });

    }

    const showUserDetailModal = (record: any) => {
        dispatch(setConfig({ loading: true }));
        userApi
            .getUserDetail({ accountId: record.account_id })
            .then((r: any) => {
                setUserDetailModal({
                    visible: true,
                    key: Date.now(),
                    detail: r.data.account,
                });
                dispatch(setConfig({ loading: false }));
            })
            .catch(() => dispatch(setConfig({ loading: false })));
    }

    const showPayoutDetailModal = (record: any) => {
        // dispatch(setConfig({ loading: true }));
        // const begin = record.apply_time < record.status_time ? record.apply_time_str : record.status_time_str;
        // const end = record.apply_time < record.status_time ? record.status_time_str : record.apply_time_str;

        // creativeRevenueApi
        //     .earningsFundFlow({ account_id: record.account_id, biz_type: 2, begin, end })
        //     .then((r: any) => {
        //         //
        //         if (r.data.list?.records.length) {
        //             const item = r.data.list?.records[0]
        //             const detailData = {
        //                 withdraw_method: '支付宝',
        //                 alipay_name: item.cert_name,
        //                 alipay_account: item.alipay_user_no,
        //                 status: record.flow_status,
        //             }
        //             setCurrentWithdrawal(detailData);
        //             setWithdrawalModalVisible(true);
        //         }

        //         dispatch(setConfig({ loading: false }));
        //     })
        //     .catch(() => dispatch(setConfig({ loading: false })));
        
        const detailData = {
            withdraw_method: '支付宝',
            alipay_name: record.cert_name,
            alipay_account: record.alipay_user_no,
        }
        setCurrentWithdrawal(detailData);
        setWithdrawalModalVisible(true);
    }

    const hidePayoutDetailModal = () => {
        setWithdrawalModalVisible(false);
        setCurrentWithdrawal(null);
    };

    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const getCheckboxProps = (record: any) => {
        return {
            disabled: record.payout_status !== 2,
            name: record.account_id,
        }
    }

    useEffect(() => {
        if (isInitialized) {
            getData({ current: 1 });
        }
    }, [filter]);

    // 跳转到媒立方
    const toMlf = (
        id: number,
        record?: any,
        channelID?: string
        ) => {
        releaseListApi
            .toMlf('mlf_detail_url', { mlf_id: id })
            .then((r: any) => {
                window.open(r.data.url);
            })
            .catch((error) => {

            });
    };

    const columns = [
        {
            title: '序号',
            key: 'seq',
            render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
            width: 60,
        },
        {
            title: '账号昵称',
            dataIndex: 'nick_name',
            key: 'nick_name',
            render: (text: string, record: any) => (
                <div>
                    <a onClick={() => showUserDetailModal(record)}>{text}</a>
                    <Icon type="wallet" onClick={() => showPayoutDetailModal(record)} style={{ marginLeft: '4px' }} />
                </div>
            ),
        },
        {
            title: '账号类型',
            dataIndex: 'author_type_name',
            key: 'author_type_name',
            width: 120,
            render(text: string) {
                return text
            }
        },
        {
            title: '提现金额（元）',
            dataIndex: 'apply_amount',
            key: 'apply_amount',
            width: 120,
            render: (text: number, record: any) => {
                if (!text && text !== 0) return '-';
                return (<PermA perm="earnings_withdraw_request:detail" onClick={() => { showUserRewardList(record) }}>{ Number.isInteger(text) ? text : text.toFixed(2) }</PermA>);
            },
        },
        {
            title: '操作人',
            dataIndex: 'processor_id',
            key: 'processor_id',
            width: 140,
        },
        {
            title: '操作时间',
            dataIndex: 'status_time_str',
            key: 'status_time_str',
            width: 160,
            render: (text: string, record: any) => (
                <a onClick={() => getOperateLog(record)}>{text || '-'}</a>
            ),
        },
        
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            width: 140,
            render: (text: any, record: any) => {
                return (<div>
                    <a onClick={() => showReason(record)}>查看原因</a>
                </div>)
            },
        },
    ];

    const detailColumns = [
        {
            title: '序号',
            dataIndex: 'id',
            key: 'id',
            width: 50,
            render: (text: any, record: any, index: number) => {
                return index + 1;
            }
        },
        {
            title: '金额',
            dataIndex: 'amount',
            key: 'amount',
            width: 100,
        },
        {
            title: '收益类型',
            dataIndex: 'fee_type_str',
            key: 'fee_type_str',
            width: 100,
        },
        {
            title: '稿费等级',
            dataIndex: 'fee_grade_str',
            key: 'fee_grade_str',
            width: 100,
        },
        {
            title: '关联稿件',
            dataIndex: 'list_title',
            key: 'list_title',
            render: (text: any, record: any) => {
                return (<a onClick={() => handleShowArticle(record)}>{text}</a>)
            },
        },
        {
            title: '稿件发布时间',
            key: 'release_time_str',
            dataIndex: 'release_time_str',
            width: 120,
        },
        {
            title: '收益获得时间',
            key: 'income_time_str',
            dataIndex: 'income_time_str',
            width: 120,
        },
    ];

    return (
        <div>
            <Row style={{ marginBottom: 16 }}>
                <Col span={14}>
                    <Form layout="inline">
                        <Form.Item>
                            <Select
                                value={filter.author_type}
                                onChange={(value) => handleFilterChange('author_type', value)}
                                style={{ width: 120 }}
                                placeholder="作者类型"
                            >
                            <Select.Option value="">账号类型</Select.Option>
                            <Select.Option value={1}>潮客</Select.Option>
                            <Select.Option value={2}>潮鸣号</Select.Option>
                            </Select>
                        </Form.Item>
                    </Form>
                </Col>
                <Col span={10} style={{ textAlign: 'right' }}>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                        <Select
                            value={filter.search_type}
                            onChange={(value) => handleFilterChange('search_type', value)}
                            style={{ width: 130, marginRight: 8 }}
                        >
                            <Select.Option value={1}>昵称或小潮号</Select.Option>
                            <Select.Option value={3}>用户ID</Select.Option>
                        </Select>
                        {filter.search_type === 3 ? (
                            <Input
                                value={search.account_id}
                                onChange={(e) => setSearch({ ...search, account_id: e.target.value })}
                                placeholder={"请输入ID"}
                                style={{ width: 200, marginRight: 8 }}
                                onKeyPress={handleKey}
                            />
                        ) : (
                            <Select
                                value={search.account_id}
                                onChange={(value) => {
                                    setSearch({ ...search, account_id: value as string });
                                }}
                                onSearch={handleAuthorSearch}
                                placeholder="请输入昵称或小潮号"
                                style={{ width: 200, marginRight: 8 }}
                                showSearch
                                allowClear={true}
                                filterOption={false}
                                >
                                {authorSuggestions.map((d: any) => (
                                    <Select.Option
                                        style={{
                                            whiteSpace: 'pre-wrap',
                                        }}
                                        key={d.id}
                                        value={d.id}
                                    >
                                    {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] || ''}${
                                        d.nick_name
                                    } | 小潮号：${d.chao_id}`}
                                    </Select.Option>
                                ))}
                            </Select>
                        )}
                        <Button type="primary" onClick={handleSearch}>
                            <Icon type="search" /> 搜索
                        </Button>
                    </div>
                </Col>
            </Row>

            <Table
                func="withdrawNotPassedList"
                index="list"
                rowKey="id"
                filter={getFilter()}
                columns={columns}
                pagination={true}
                multi={false}
                selectedRowKeys={selectedRowKeys}
                onSelectChange={handleSelectChange}
                getCheckboxProps={getCheckboxProps}
                tableProps={{ scroll: { x: 1000 } }}
            />

            <Modal
                visible={userDetailModal.visible}
                key={userDetailModal.key}
                title="用户详情"
                width={800}
                onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
                onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
            >
                {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
            </Modal>

            <Drawer
                visible={userRewardList.visible}
                title={`${userRewardList.record ? userRewardList.record.nick_name : ''}的提现收益明细`}
                skey={`UserRewardListDrawer`}
                maskClosable={true}
                onClose={handleUserRewardListClose}
                footer={(<>
                    <Row justify={'end'} type={'flex'}>
                        <Col span={6}>
                            <div style={{ fontSize: '18px' }}>
                                {`金额总计：${userRewardList.amount}元`}
                            </div>
                        </Col>
                    </Row>
                </>)}
            >
                <div>
                    <NTable bordered dataSource={userRewardList.list} columns={detailColumns} pagination={false} />
                </div>

                <PreviewMCN
                    { ...preview }
                    onClose={() => setPreview({ ...preview, visible: false, data: {} })} 
                />
            </Drawer>

            <Modal
                visible={operateLog.visible}
                title="操作日志"
                cancelText={null}
                onCancel={() => setOperateLog({ ...operateLog, visible: false })}
                onOk={() => setOperateLog({ ...operateLog, visible: false })}
            >
                <div>
                    <Timeline style={{ marginTop: 20 }}>
                    {operateLog.logs?.map((v: any, i: number) => [
                        <Timeline.Item className="timeline-dot-big" data-show={v.date} key={`time${i}`}>
                        &nbsp;
                        </Timeline.Item>,
                        v.actions?.map((action: any, index: number) => (
                        <Timeline.Item
                            className="timeline-dot"
                            data-show={action.time}
                            key={`time${i}-action${index}`}
                        >
                            {action.user}&emsp;{action.action}
                        </Timeline.Item>
                        )),
                    ])}
                    </Timeline>
                </div>
            </Modal>

            <Modal
                title="提现方式"
                visible={withdrawalModalVisible}
                onCancel={hidePayoutDetailModal}
                footer={[
                    <Button key="close" onClick={hidePayoutDetailModal}>
                    关闭
                    </Button>
                ]}
                width={400}
                >
                {currentWithdrawal && (
                    <div>
                        <p>提现方式：{currentWithdrawal.withdraw_method}</p>
                        <p>支付宝姓名：{currentWithdrawal.alipay_name}</p>
                        <p>支付宝账号：{currentWithdrawal.alipay_account}</p>
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default NotPassed; 