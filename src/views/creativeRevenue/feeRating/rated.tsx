import React, { useState, useEffect, memo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getTableList, setTableList } from '@app/action/tableList';
import { Table } from '@components/common';
import SortableColumn from '@components/common/sortableColumn';
import PreviewMCN from '@components/common/previewMCN';
import { getCrumb, UserDetail, requirePerm, requirePerm4Function } from '@utils/utils';
import SourceInfoModal from '@app/views/creativeRevenue/feeRating/components/SourceInfoModal';
import {
  Button,
  Divider,
  Col,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Dropdown,
  Menu,
  Timeline,
  InputNumber,
  AutoComplete,
  Tooltip,
} from 'antd';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';
import moment from 'moment';
import { CommonObject } from '@app/types';
import { PermA, PermButton } from '@app/components/permItems';
import { setMenuHook } from '@app/utils/utils';
import { debounce } from 'lodash';
import { userApi, communityApi, creativeRevenueApi as api } from '@app/api';
import { setConfig } from '@app/action/config';
import { setTableCache } from '@app/action/tableCache';
import SpecifyFeeModal from './components/SpecifyFeeModal';
import FeeRatingModal from './components/FeeRatingModal';
import BatchRatingModal from './components/BatchRatingModal';
import { useStore } from 'react-redux';

interface FilterState {
  fee_type: number | string;
  rating_status: boolean | string;
  fee_grade: number | string;
  author_type: number | string;
  search_type: number;
  keyword: string;
  account_id: string;
  sort_by?: number;
  sort_asc?: number;
  spread_index: string;
}

export default function Rated(props: any) {
  const dispatch = useDispatch();
  const history = useHistory();
  const location = useLocation();
  const match = useRouteMatch();

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);
  const { session } = useStore().getState();
  const { total, current, size, records = [] } = useSelector((state: any) => state.tableList);

  // Local state
  const [filter, setFilter] = useState<FilterState>({
    fee_type: '',
    rating_status: '',
    fee_grade: '',
    author_type: '',
    search_type: 1,
    keyword: '',
    account_id: '',
    spread_index: '',
  });

  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [ratingModalVisible, setRatingModalVisible] = useState(false);
  const [batchRatingModalVisible, setBatchRatingModalVisible] = useState(false);
  const [ratingRecord, setRatingRecord] = useState<any>({});
  const [isInitialized, setIsInitialized] = useState(false);

  // 搜索状态
  const [search, setSearch] = useState<{
    keyword: string;
    author_id?: string;
  }>({
    keyword: '',
  });

  // 用户详情弹窗
  const [userDetailModal, setUserDetailModal] = useState({
    visible: false,
    key: Date.now(),
    detail: null,
  });

  // 操作日志弹窗
  const [operateLog, setOperateLog] = useState<{
    visible: boolean;
    logs: any[];
  }>({
    visible: false,
    logs: [],
  });

  // 作者搜索建议
  const [authorSuggestions, setAuthorSuggestions] = useState<any[]>([]);

  // 指定稿费弹窗
  const [specifyFeeVisible, setSpecifyFeeVisible] = useState(false);

  // 上榜详情弹窗
  const [rankDetailModal, setRankDetailModal] = useState<{
    visible: boolean;
    data: any;
    loading: boolean;
  }>({
    visible: false,
    data: null,
    loading: false,
  });

  // 预览稿件弹窗
  const [preview, setPreview] = useState({
    visible: false,
    data: {},
    skey: Date.now(),
  });

  // 源稿信息弹窗
  const [sourceInfo, setSourceInfo] = useState({
    visible: false,
    sourceInfo: {
      newsId: '',
      contentType: '',
      contentLink: '',
    },
  });

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      getData({ current: tableCache.current, size: tableCache.size });
    } else {
      getData({ current: 1 });
    }

    setIsInitialized(true);
  }, []);

  // 获取数据
  const getData = (overlap: CommonObject = {}) => {
    const params = { ...getFilter(), ...overlap, review_status: 0 };
    dispatch(getTableList('getRatedList', 'list', params));
  };

  // 获取过滤条件
  const getFilter = () => {
    const { current, size } = tableList;
    const filters: CommonObject = { current, size };

    // 添加所有筛选字段
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== undefined) {
        filters[key] = value;
      }
    });

    return filters;
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    if (key === 'search_type') {
      // 搜索类型变化时，清空关键词和作者建议
      setFilter({
        ...filter,
        [key]: value,
        keyword: '',
      });
      setSearch({
        keyword: '',
        author_id: undefined,
      });
      setAuthorSuggestions([]);
    } else {
      setFilter({
        ...filter,
        [key]: value,
      });
    }
  };

  // 监听 filter 变化 - 优化：合并所有筛选条件变化
  useEffect(() => {
    if (isInitialized) {
      getData({ current: 1 });
    }
  }, [filter]);

  // 处理搜索 - 添加防抖
  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      handleSearch();
    }
  };

  // 处理搜索
  const handleSearch = () => {
    if (filter.search_type === 2) {
      // 作者搜索，使用 account_id
      setFilter({
        ...filter,
        account_id: search.author_id || '',
        keyword: '',
      });
    } else if (filter.search_type === 3) {
      // ID搜索，使用 keyword
      setFilter({
        ...filter,
        keyword: search.keyword,
        account_id: '',
      });
    } else {
      // 标题搜索，使用 list_title
      setFilter({
        ...filter,
        keyword: search.keyword,
        account_id: '',
      });
    }
  };

  // 处理作者搜索建议
  const handleAuthorSearch = debounce((value: string) => {
    if (value && filter.search_type === 2) {
      // 调用接口获取作者建议列表
      communityApi
        .recommendAccount_Search({ keyword: value })
        .then((res: any) => {
          setAuthorSuggestions(res.data?.list || []);
        })
        .catch(() => {
          setAuthorSuggestions([]);
        });
    } else {
      setAuthorSuggestions([]);
    }
  }, 300);

  // 处理排序
  const handleSort = (sortAsc: number, sortBy: number) => {
    let sort_asc;
    if (sortAsc === 0) {
      sort_asc = 'DESC';
    } else if (sortAsc === 1) {
      sort_asc = 'ASC';
    } else {
      sort_asc = '';
    }
    setFilter({
      ...filter,
      spread_index: sort_asc,
    });
  };

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    dispatch(setConfig({ loading: true }));
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUserDetailModal({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        });
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  // 获取操作日志
  const getOperateLog = (record: any) => {
    console.log('获取操作日志，记录ID：', record.id);
    dispatch(setConfig({ loading: true }));
    api
      .getOperateLog({
        id: record.id,
      })
      .then((r: any) => {
        console.log(r);
        // 模拟数据结构，与参考文件保持一致
        const mockLogs = r.data?.list?.records || [];
        dispatch(setConfig({ loading: false }));

        setOperateLog({
          visible: true,
          logs: mockLogs,
        });
      })
      .catch(() => dispatch(setConfig({ loading: false })));
  };

  // 处理评级
  const handleRating = (record?: any) => {
    if (record) {
      // ✅ 单个评级
      setRatingRecord(record);
      setRatingModalVisible(true);
    } else {
      // ✅ 批量评级
      if (selectedRowKeys.length === 0) {
        message.error('请选择数据');
        return;
      }
      setBatchRatingModalVisible(true);
    }
  };

  // 评级成功回调
  const handleRatingSuccess = () => {
    setRatingModalVisible(false);
    setRatingRecord({});

    // ⚡ 刷新数据
    getData();
  };

  // 批量评级成功回调
  const handleBatchRatingSuccess = () => {
    // 🐛 清空选中状态
    setSelectedRowKeys([]);

    // ⚡ 刷新数据
    getData();
  };

  // 取消评级
  const handleRatingCancel = () => {
    setRatingModalVisible(false);
    setRatingRecord({});
  };

  // 取消批量评级
  const handleBatchRatingCancel = () => {
    setBatchRatingModalVisible(false);
  };

  // 处理送审
  const handleSubmitAudit = (record?: any) => {
    let ids = '';
    const submitFunc = () => {
      setLoading(true);
      api
        .batchAudit({
          ids: ids,
        })
        .then(() => {
          message.success('操作成功');
          getData();
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    };

    if (record) {
      ids = record.id;
      // 单个送审
      if (!record.fee_grade) {
        message.error('该稿件还未评级');
        return;
      }
      Modal.confirm({
        title: '确定提交审批？',
        onOk: submitFunc,
      });
    } else {
      // 批量送审
      ids = selectedRowKeys.join(',');
      if (selectedRowKeys.length === 0) {
        message.error('请选择数据');
        return;
      }

      // 检查是否有未评级的
      const unratedCount = selectedRowKeys.filter((key) => {
        const record = records.find((r: any) => r.id === key);
        return !record?.fee_grade;
      }).length;

      if (unratedCount > 0) {
        message.error(`已选数据中有${unratedCount}条还未评级`);
        return;
      }

      Modal.confirm({
        title: `已选择${selectedRowKeys.length}条数据，确定批量提交审批？`,
        onOk: submitFunc,
      });
    }
  };

  // 处理删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确定删除？',
      onOk: () => {
        setLoading(true);
        api
          .deleteFee({ id: record.id })
          .then(() => {
            message.success('删除成功');
            getData();
            setLoading(false);
          })
          .catch(() => {
            console.log('删除失败');
            setLoading(false);
          });
      },
    });
  };

  // 处理指定稿费
  const handleSpecifyFee = () => {
    setSpecifyFeeVisible(true);
  };

  // 处理指定稿费确认
  const handleSpecifyFeeOk = () => {
    setSpecifyFeeVisible(false);
    getData();
  };

  // 处理指定稿费取消
  const handleSpecifyFeeCancel = () => {
    setSpecifyFeeVisible(false);
  };

  // ✅ 处理上榜详情
  const handleFeeDetail = async (record: any) => {
    try {
      const response = await api.getRankDetail({
        article_id: record.article_id,
      });

      setRankDetailModal({
        visible: true,
        data: response.data,
        loading: false,
      });
    } catch (error) {
      console.error('获取上榜详情失败：', error);
      setRankDetailModal({
        visible: false,
        data: null,
        loading: false,
      });
    }
  };

  // 显示源稿信息弹窗
  const showSourceInfo = (record: any) => {
    console.log(record);
    // 这里可以根据实际情况从record中获取源稿信息
    // 如果record中没有相关字段，可以使用默认值或者通过API获取
    setSourceInfo({
      visible: true,
      sourceInfo: {
        ...record.ugc_article,
      },
    });
  };

  // 处理导出数据
  const handleExport = () => {
    Modal.confirm({
      title: '单次最多可导出2000行数据',
      content: '如果当前列表数量超出上限，仅导出前2000行 ',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        performExport();
      },
    });
  };

  // 执行导出操作
  const performExport = () => {
    // ✅ 创建导出参数，排除分页信息
    const exportParams: CommonObject = { review_status: 0 };

    // 添加所有筛选字段（排除分页参数）
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== undefined) {
        exportParams[key] = value;
      }
    });

    dispatch(setConfig({ loading: true }));
    api
      .export(exportParams)
      .then((res: any) => {
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(res.data);
        a.download = `潮新闻-创作收益-稿费评级-待评级-${moment().format('YYYYMMDD')}.xlsx`;
        a.click();
        dispatch(setConfig({ loading: false }));
      })
      .catch(() => {
        dispatch(setConfig({ loading: false }));
      });
  };

  // 处理批量选择
  const handleSelectChange = (selectedKeys: any[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 获取序号
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  // 获取列配置
  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
      width: 60,
    },
    {
      title: (
        <div>
          稿件标题&nbsp;
          <Tooltip title="针对取稿稿费，此处显示媒立方稿件标题" placement="top">
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'list_title',
      key: 'list_title',
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <a
            onClick={() => {
              setPreview({
                visible: true,
                data: {
                  article_id: record.article_id,
                  doc_type: 10,
                },
                skey: Date.now(),
              });
            }}
          >
            {text}
            {record.mlf_id && (
              <Button
                type="dashed"
                size="small"
                style={{ padding: 3, paddingTop: 0, paddingBottom: 0, marginLeft: 5 }}
                onClick={(e) => {
                  e.stopPropagation();
                  showSourceInfo(record);
                }}
              >
                源
              </Button>
            )}
          </a>
        </div>
      ),
    },
    {
      title: (
        <div>
          频道&nbsp;
          <Tooltip title="针对取稿稿费，此处显示媒立方稿件所在频道" placement="top">
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'channel_name',
      key: 'channel_name',
      width: 100,
    },
    {
      title: '等级',
      dataIndex: 'fee_grade_str',
      key: 'fee_grade_str',
      width: 80,
    },
    {
      title: '金额（元）',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      render: (text: number) => {
        if (!text && text !== 0) return '-';
        return Number.isInteger(text) ? text : text.toFixed(2);
      },
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '105px',
          }}
        >
          <SortableColumn
            title="传播指数"
            sort_by={filter.sort_by || 0}
            currentSortBy={1}
            pointerEvents={!filter.keyword}
            onChange={handleSort}
          />
          <Tooltip
            title="稿件发布48小时内的传播情况（包括浏览量及转赞评）"
            placement="top"
            style={{ marginLeft: 10 }}
          >
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      ),
      dataIndex: 'spread_index',
      key: 'spread_index',
      width: 120,
      render: (text: any, record: any) => <div>{text}</div>,
    },
    {
      title: '稿费类型',
      dataIndex: 'fee_type_str',
      key: 'fee_type_str',
      width: 110,
      render: (text: string, record: any) => {
        if (!text) return '-';

        // ✅ 上榜奖励类型添加详情按钮
        if (record.fee_type === 3) {
          return (
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <span>{text}</span>
              <Button
                type="dashed"
                size="small"
                style={{ padding: 3, paddingTop: 0, paddingBottom: 0 }}
                onClick={() => handleFeeDetail(record)}
              >
                详
              </Button>
            </div>
          );
        }
        return text;
      },
    },
    {
      title: '作者',
      dataIndex: 'nick_name',
      key: 'nick_name',
      width: 120,
      render: (text: string, record: any) => (
        <a onClick={() => showUserDetailModal(record)}>{text}</a>
      ),
    },
    {
      title: '发布时间',
      dataIndex: 'release_time_str',
      key: 'release_time_str',
      width: 160,
      render: (text: string) => text || '-',
    },
    {
      title: '最后操作时间',
      dataIndex: 'update_time_str',
      key: 'update_time_str',
      width: 160,
      render: (text: string, record: any) => (
        <a onClick={() => getOperateLog(record)}>{text || '-'}</a>
      ),
    },
    {
      title: '操作',
      key: 'op',
      width: 180,
      render: (text: any, record: any, i: number) => (
        <span>
          <PermA perm="earnings_manuscript_income:update_fee_grade" onClick={() => handleRating(record)}>
            评级
          </PermA>
          <Divider type="vertical" />

          <PermA perm="earnings_manuscript_income:send_for_review" onClick={() => handleSubmitAudit(record)}>
            送审
          </PermA>
          <Divider type="vertical" />
          <PermA perm="earnings_manuscript_income:delete" onClick={() => handleDelete(record)}>
            删除
          </PermA>
        </span>
      ),
    },
  ];

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <PermButton perm="earnings_manuscript_income:add_manuscript" onClick={handleSpecifyFee} style={{ marginRight: 8 }}>
            <Icon type="plus-circle" /> 指定稿费
          </PermButton>
          <PermButton perm="earnings_manuscript_income:export_income" onClick={handleExport}>
            <Icon type="export" /> 导出数据
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item>
                <Select
                  value={filter.fee_type}
                  onChange={(value) => handleFilterChange('fee_type', value)}
                  style={{ width: 120 }}
                  placeholder="稿费类型"
                >
                  <Select.Option value="">稿费类型</Select.Option>
                  <Select.Option value={1}>取稿稿费</Select.Option>
                  <Select.Option value={2}>指定稿费</Select.Option>
                  <Select.Option value={3}>上榜奖励</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.rating_status}
                  onChange={(value) => handleFilterChange('rating_status', value)}
                  style={{ width: 120 }}
                  placeholder="评级状态"
                >
                  <Select.Option value="">评级状态</Select.Option>
                  <Select.Option value="true">已评级</Select.Option>
                  <Select.Option value="false">未评级</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.fee_grade}
                  onChange={(value) => handleFilterChange('fee_grade', value)}
                  style={{ width: 120 }}
                  placeholder="稿费等级"
                >
                  <Select.Option value="">稿费等级</Select.Option>
                  <Select.Option value={1}>甲等</Select.Option>
                  <Select.Option value={2}>乙等</Select.Option>
                  <Select.Option value={3}>丙等</Select.Option>
                  <Select.Option value={4}>丁等</Select.Option>
                  <Select.Option value={5}>戊等</Select.Option>
                  <Select.Option value={0}>其他</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Select
                  value={filter.author_type}
                  onChange={(value) => handleFilterChange('author_type', value)}
                  style={{ width: 120 }}
                  placeholder="作者类型"
                >
                  <Select.Option value="">账号类型</Select.Option>
                  <Select.Option value={1}>潮客</Select.Option>
                  <Select.Option value={2}>潮鸣号</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item>
                {requirePerm4Function(
                  session,
                  'earnings_manuscript_income:update_fee_grade'
                )(
                  <Button
                    onClick={() => handleRating()}
                    disabled={selectedRowKeys.length === 0}
                    style={{ marginRight: 8 }}
                    loading={loading}
                  >
                    批量评级
                  </Button>
                )}
                {requirePerm4Function(
                  session,
                  'earnings_manuscript_income:send_for_review'
                )(
                  <Button
                    onClick={() => handleSubmitAudit()}
                    disabled={selectedRowKeys.length === 0}
                    loading={loading}
                  >
                    批量送审
                  </Button>
                )}
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Select
                value={filter.search_type}
                onChange={(value) => handleFilterChange('search_type', value)}
                style={{ width: 100, marginRight: 8 }}
              >
                <Select.Option value={1}>稿件标题</Select.Option>
                <Select.Option value={2}>作者</Select.Option>
                <Select.Option value={3}>ID</Select.Option>
              </Select>
              {filter.search_type === 2 ? (
                <Select
                  value={search.author_id}
                  onChange={(value) => {
                    setSearch({ ...search, author_id: value as string });
                  }}
                  onSearch={handleAuthorSearch}
                  placeholder="输入昵称进行搜索"
                  style={{ width: 200, marginRight: 8 }}
                  showSearch
                  allowClear={true}
                  filterOption={false}
                >
                  {authorSuggestions.map((d: any) => (
                    <Select.Option
                      style={{
                        whiteSpace: 'pre-wrap',
                      }}
                      key={d.id}
                      value={d.id}
                    >
                      {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] || ''}${
                        d.nick_name
                      } | 小潮号：${d.chao_id}`}
                    </Select.Option>
                  ))}
                </Select>
              ) : (
                <Input
                  value={search.keyword}
                  onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
                  placeholder={filter.search_type === 3 ? '请输入ID' : '请输入搜索内容'}
                  style={{ width: 160, marginRight: 8 }}
                  onKeyPress={handleKey}
                />
              )}
              <Button type="primary" onClick={handleSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          func="getRatedList"
          index="list"
          rowKey="id"
          filter={getFilter()}
          columns={columns}
          pagination={true}
          multi={true}
          selectedRowKeys={selectedRowKeys}
          onSelectChange={handleSelectChange}
        />

        {/* 用户详情弹窗 */}
        <Modal
          visible={userDetailModal.visible}
          key={userDetailModal.key}
          title="用户详情"
          width={800}
          onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
          onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
        >
          {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
        </Modal>

        {/* 操作日志弹窗 */}
        <Modal
          visible={operateLog.visible}
          title="操作日志"
          onCancel={() => setOperateLog({ ...operateLog, visible: false })}
          onOk={() => setOperateLog({ ...operateLog, visible: false })}
        >
          <div>
            <Timeline style={{ marginTop: 20 }}>
              {operateLog.logs?.map((v: any, i: number) => [
                <Timeline.Item
                  className="timeline-dot"
                  data-show={moment(v.operation_time).format('HH:mm:ss')}
                  key={`time${i}-action`}
                >
                  {v.operator}&emsp;{v.action}
                </Timeline.Item>,
              ])}
            </Timeline>
          </div>
        </Modal>

        {/* 单个评级弹窗 */}
        <FeeRatingModal
          visible={ratingModalVisible}
          title="评级"
          initialGrade={ratingRecord?.fee_grade || ''}
          record={ratingRecord}
          onCancel={handleRatingCancel}
          onSuccess={handleRatingSuccess}
        />

        {/* 批量评级弹窗 */}
        <BatchRatingModal
          visible={batchRatingModalVisible}
          selectedCount={selectedRowKeys.length}
          selectedIds={selectedRowKeys}
          onCancel={handleBatchRatingCancel}
          onSuccess={handleBatchRatingSuccess}
        />

        {/* 指定稿费弹窗 */}
        <SpecifyFeeModal
          visible={specifyFeeVisible}
          onOk={handleSpecifyFeeOk}
          onCancel={handleSpecifyFeeCancel}
        />

        {/* 上榜详情弹窗 */}
        <Modal
          visible={rankDetailModal.visible}
          title="上榜详情"
          width={600}
          onCancel={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
          footer={[
            <Button
              key="ok"
              type="primary"
              onClick={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
            >
              确定
            </Button>,
          ]}
          destroyOnClose={true}
        >
          {rankDetailModal.loading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>
              <Icon type="loading" />
            </div>
          ) : (
            <div style={{ padding: '20px 0' }}>
              {rankDetailModal.data?.rank_name ? (
                <div>
                  <a
                    href={rankDetailModal.data?.rank_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ color: '#1890ff', textDecoration: 'none', fontSize: '14px' }}
                  >
                    {rankDetailModal.data.rank_name}
                  </a>
                </div>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                  暂无上榜详情
                </div>
              )}
            </div>
          )}
        </Modal>

        {/* 预览稿件弹窗 */}
        <PreviewMCN
          visible={preview.visible}
          skey={Date.now()}
          data={preview.data}
          onClose={() => setPreview({ ...preview, visible: false })}
        />

        {/* 源稿信息弹窗 */}
        <SourceInfoModal
          visible={sourceInfo.visible}
          sourceInfo={sourceInfo.sourceInfo}
          onCancel={() => setSourceInfo({ ...sourceInfo, visible: false })}
        />
      </div>
    </>
  );
}
