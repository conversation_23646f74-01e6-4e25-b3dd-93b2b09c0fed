import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Input, Button, message } from 'antd';
import { creativeRevenueApi as api } from '@app/api';
import { FormComponentProps } from 'antd/es/form';

interface FeeRatingModalProps extends FormComponentProps {
  visible: boolean;
  title?: string;
  initialGrade?: string | Number;
  initialAmount?: string;
  record: any; // 评级记录ID
  onSuccess: () => void; // 评级成功回调
  onCancel: () => void;
}

const FeeRatingModal: React.FC<FeeRatingModalProps> = ({
  form,
  visible,
  title = '稿费评级',
  initialGrade = '',
  initialAmount = '',
  record,
  onSuccess,
  onCancel,
}) => {
  const { getFieldDecorator, getFieldValue, validateFields, resetFields, setFieldsValue } = form;
  const [loading, setLoading] = useState<boolean>(false);

  // 当弹窗打开时重置表单
  useEffect(() => {
    if (visible) {
      console.log(initialGrade);
      // 设置初始值
      setFieldsValue({
        fee_grade: initialGrade.toString(),
        custom_amount: initialGrade === 0 ? record.amount : '',
      });
    }
  }, [visible, initialGrade, initialAmount, setFieldsValue]);

  // 处理确认
  const handleOk = async () => {
    validateFields(async (err, values) => {
      if (err) return;

      const { fee_grade, custom_amount } = values;

      try {
        setLoading(true);

        // 构建API参数
        const params = {
          ids: record.id,
          fee_grade: fee_grade,
          amount: fee_grade === '0' ? custom_amount : '',
        };

        // 调用API进行评级
        await api.batchUpdateFeeGrade(params);
        message.success('评级成功');

        // 关闭弹窗并通知父组件刷新数据
        handleCancel();
        onSuccess();
      } catch (error) {
        console.error('评级失败：', error);
        message.error('评级失败，请重试');
      } finally {
        setLoading(false);
      }
    });
  };

  // 处理取消
  const handleCancel = () => {
    resetFields();
    onCancel();
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="确定"
      cancelText="取消"
      width={480}
      confirmLoading={loading}
    >
      <Form layout="vertical">
        <Form.Item label="稿费等级" required>
          {getFieldDecorator('fee_grade', {
            initialValue: initialGrade,
            rules: [
              {
                required: true,
                message: '请选择稿费等级',
              },
            ],
          })(
            <Radio.Group>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <Radio value="1">甲等 (500元)</Radio>
                <Radio value="2">乙等 (300元)</Radio>
                <Radio value="3">丙等 (100元)</Radio>
                <Radio value="4">丁等 (50元)</Radio>
                <Radio value="5">戊等 (10元)</Radio>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Radio value="0">其他</Radio>
                  {getFieldDecorator('custom_amount', {
                    initialValue: initialAmount,
                    rules: [
                      {
                        validator: (_rule, value, callback) => {
                          // 只有当选择"其他"等级时才验证自定义金额
                          if (getFieldValue('fee_grade') === '0') {
                            if (!value) {
                              message.error('请输入稿费金额');
                              callback('请输入稿费金额');
                              return;
                            }

                            const numValue = Number(value);
                            if (isNaN(numValue) || numValue <= 0) {
                              message.error('请输入有效的稿费金额');
                              callback('请输入有效的稿费金额');
                              return;
                            }

                            if (numValue > 999) {
                              message.error('稿费金额不能超过999元');
                              // 自动设置为999
                              form.setFieldsValue({ custom_amount: 999 });
                              callback();
                              return;
                            }
                          }
                          callback();
                        }
                      }
                    ]
                  })(
                    <Input
                      placeholder="输入稿费金额"
                      style={{ width: '120px' }}
                      type="number"
                      min={1}
                      max={999}
                      onChange={(e) => {
                        const value = Number(e.target.value);
                        if (value > 999) {
                          form.setFieldsValue({ custom_amount: 999 });
                        }
                      }}
                    />
                  )}
                </div>
              </div>
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<FeeRatingModalProps>()(FeeRatingModal);
