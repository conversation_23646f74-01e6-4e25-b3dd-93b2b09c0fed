import React, { useState, useEffect } from 'react';
import {
  Button,
  Col,
  Form,
  Icon,
  Input,
  Row,
  Select,
  Table,
  Radio,
  Tooltip,
  message,
  Modal,
  DatePicker,
  Pagination,
} from 'antd';
import { getCrumb, UserDetail, setMenuHook } from '@utils/utils';
import SortableColumn from '@components/common/sortableColumn';
import { userApi, communityApi, creativeRevenueApi } from '@app/api';
import { Link, useHistory } from 'react-router-dom';
import _ from 'lodash';
import { ColumnProps, SortOrder } from 'antd/lib/table';
import moment from 'moment';
import { useDispatch } from 'react-redux';
import { PermButton } from '@app/components/permItems';
import PreviewMCN from '@app/components/common/previewMCN';
import SourceInfoModal from '@app/views/creativeRevenue/feeRating/components/SourceInfoModal';

interface FilterState {
  account_type: string;
  withdrawal_method: string;
  keyword: string;
  account_id?: string;
  sort_by: number;  // 1: 待提现金额, 2: 已提现金额
  sort_asc: number; // 1: 升序, 0: 降序, -1: 重置排序
  begin?: string;   // 日期范围开始
  end?: string;     // 日期范围结束
  current: number;
  size: number;
  biz_type?: string; // 添加业务类型字段
  fee_type?: string; // 添加费用类型字段
  sort_field?: string; // 排序字段名：unwithdrawn_balance 或 withdrawn_balance
  order?: string; // 排序方向：asc 或 desc
}

interface AccountRecord {
  id: number;
  account_id: string;
  nick_name: string;
  author_type: number;
  author_type_str: string;
  withdrawn_balance: number;
  unwithdrawn_balance: number;
  alipay_user_no: string;
  chao_id?: string; // 添加 chao_id 字段
  total_balance?: number;
  expired_balance?: number;
  freezing_balance?: number;
  withdrawable_balance?: number;
  created_time?: number;
  last_updated?: number;
}

interface ApiResponse<T> {
  data: {
    list: T[];
  };
}

interface AccountOption {
  id: string;
  nick_name: string;
  cert_type: number;
  chao_id: string;
}

interface CommonResponse<T> {
  data: T;
}

interface AccountSearchResponse {
  list: AccountOption[];
}

interface TransactionRecord {
  id: number;
  nick_name: string;
  create_time: string;
  event: string;
  article_title: string;
  article_id: string;
  amount: number;
  status: 'success' | 'fail';
  status_str: string;
  alipay_real_name: string;
  alipay_account: string;
  alipay_trade_no?: string;
  success_time?: string;
  fail_reason?: string;
  fail_time?: string;
  order_id?: string;
  // 添加与 accountDetail.tsx 一致的字段
  explain: string;
  list_title: string;
  url: string;
  flow_time_str: string;
  flow_status_str: string;
  flow_status: number;
  fee_type?: number; // 添加费用类型字段
  mlf_id?: string; // 添加mlf_id字段
  ugc_article?: { // 添加ugc_article字段
    newsId: string;
    contentType: string;
    contentLink: string;
  };
}

interface AccountListResponse {
  total: number;
  size: number;
  pages: number;
  current: number;
  records: AccountRecord[];
}

interface EarningsTotal {
  withdrawable_total: number;
  withdrawn_total: number;
}

interface TransactionListResponse {
  total: number;
  size: number;
  pages: number;
  current: number;
  records: TransactionRecord[];
}

// 初始状态
const initialFilter: FilterState = {
  account_type: '',
  withdrawal_method: '',
  keyword: '',
  current: 1,
  size: 10,
  sort_by: 1,         // 1: 待提现金额
  sort_asc: 0,        // 0: 降序
  sort_field: 'unwithdrawn_balance', // 默认按待提现金额排序
};

export default function RevenueQuery(props: any) {
  // Local state
  const [activeTab, setActiveTab] = useState('account');
  const [loading, setLoading] = useState(false);
  const history = useHistory();
  const [accountData, setAccountData] = useState<AccountListResponse>({
    total: 0,
    size: 10,
    pages: 0,
    current: 1,
    records: [],
  });
  const [transactionData, setTransactionData] = useState<TransactionListResponse>({
    total: 0,
    size: 10,
    pages: 0,
    current: 1,
    records: [],
  });
  const [filter, setFilter] = useState<FilterState>(initialFilter);
  const [pageInput, setPageInput] = useState<FilterState>(initialFilter);
  
  // 添加账户搜索相关状态
  const [accountOptions, setAccountOptions] = useState<any[]>([]);
  const [transactionOptions, setTransactionOptions] = useState<any[]>([]);
  const [accountSearchValue, setAccountSearchValue] = useState<{
    label: string;
    value: string;
  } | null>(null);
  const [transactionSearchValue, setTransactionSearchValue] = useState<{
    label: string;
    value: string;
  } | null>(null);
  const [earningsTotal, setEarningsTotal] = useState<EarningsTotal>({
    withdrawable_total: 0,
    withdrawn_total: 0,
  });

  // 用户详情弹窗
  const [userDetailModal, setUserDetailModal] = useState({
    visible: false,
    key: Date.now(),
    detail: null,
  });

  // 添加提现详情弹窗状态
  const [withdrawalDetailModal, setWithdrawalDetailModal] = useState({
    visible: false,
    record: null as any
  });

  // 添加上榜详情弹窗状态
  const [rankDetailModal, setRankDetailModal] = useState<{
    visible: boolean;
    data: any;
    loading: boolean;
  }>({
    visible: false,
    data: null,
    loading: false,
  });

  // 添加预览相关的状态
  const [preview, setPreview] = useState({
    visible: false,
    data: {},
    skey: Date.now(),
  });

  // 添加源稿信息相关的状态
  const [sourceInfo, setSourceInfo] = useState({
    visible: false,
    sourceInfo: {
      newsId: '',
      contentType: '',
      contentLink: '',
    },
  });

  // 执行导出操作
  const performExport = async () => {
    const exportLimit = 5000;
    const exportCount = Math.min(activeTab === 'account' ? accountData.total : transactionData.total, exportLimit);
    
    try {
      if (activeTab === 'account') {
        const params = {
          ...(filter.account_type && { author_type: filter.account_type }),
          ...(filter.withdrawal_method && { binding: filter.withdrawal_method === '1' }),
          ...(filter.account_id && { account_id: filter.account_id }),
          ...(filter.sort_by && { sort_field: filter.sort_by === 1 ? 'unwithdrawn_balance' : 'withdrawn_balance' }),
          ...(filter.sort_asc !== undefined && { order: filter.sort_asc === 1 ? 'asc' : 'desc' }),
          export_flag: 1,
        };
        
        communityApi.exportEarningsAccountList(params)
          .then((res) => {
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(res.data);
            a.download = `潮新闻-创作收益-账号查询${moment().format('YYYYMMDD')}.xlsx`;
            a.click();
          });
      } else {
        // 导出收支记录
        const params = {
          ...(filter.account_id && { account_id: filter.account_id }),
          ...(filter.biz_type && { biz_type: filter.biz_type }),
          ...(filter.fee_type && { fee_type: filter.fee_type }),
          ...(filter.begin && { begin: moment(filter.begin).format('YYYY-MM-DD HH:mm:ss') }),
          ...(filter.end && { end: moment(filter.end).format('YYYY-MM-DD HH:mm:ss') }),
        };
        
        communityApi.exportEarningsFundFlow(params)
          .then((res) => {
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(res.data);
            a.download = `潮新闻-创作收益-收支记录-${moment().format('YYYYMMDD')}.xlsx`;
            a.click();
          });
      }
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  // 处理导出数据
  const handleExport = () => {
    Modal.confirm({
      title: '单次最多可导出 5000 行数据',
      content: (
        <div>
          如果当前列表数量超出上限，仅导出前 5000 行
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        performExport();
      },
      width: 400,
      centered: true,
      maskClosable: false,
    });
  };

  // 显示用户详情
  const showUserDetailModal = (record: any) => {
    userApi
      .getUserDetail({ accountId: record.account_id })
      .then((r: any) => {
        setUserDetailModal({
          visible: true,
          key: Date.now(),
          detail: r.data.account,
        });
      });
  };

  // 处理上榜详情
  const handleFeeDetail = async (record: any) => {
    try {
      setRankDetailModal({
        ...rankDetailModal,
        loading: true,
      });
      
      const response = await creativeRevenueApi.getRankDetail({
        article_id: record.article_id,
      });

      setRankDetailModal({
        visible: true,
        data: response.data,
        loading: false,
      });
    } catch (error) {
      console.error('获取上榜详情失败：', error);
      setRankDetailModal({
        visible: false,
        data: null,
        loading: false,
      });
      message.error('获取上榜详情失败');
    }
  };

  // 修改显示提现详情函数
  const showWithdrawalDetail = (record: any) => {
    // 如果是上榜奖励类型，则显示上榜详情
    if (record.fee_type === 3) {
      handleFeeDetail(record);
      return;
    }
    
    // 其他类型的提现详情保持原有逻辑
    const detailData = {
      withdraw_method: '个人支付宝',
      cert_name: record.cert_name || record.alipay_real_name || '未知',
      alipay_user_no: record.alipay_user_no || record.alipay_account || '未绑定',
      flow_status: record.flow_status,
      order_id: record.order_id || record.alipay_trade_no || record.flow_no || '-',
      description: record.description || '未知原因',
      flow_time_str: record.flow_time_str || record.success_time || record.fail_time || '-',
    };
    
    setWithdrawalDetailModal({
      visible: true,
      record: detailData,
    });
  };

  // 关闭提现详情弹窗
  const closeWithdrawalDetail = () => {
    setWithdrawalDetailModal({
      visible: false,
      record: null,
    });
  };

  // 获取提现金额统计
  const fetchEarningsTotal = async () => {
    const response = await communityApi.getEarningsTotal();
    setEarningsTotal(response.data);
  };

  const dispatch = useDispatch();

  useEffect(() => {
    setMenuHook(dispatch, props);
    fetchEarningsTotal();
  }, []);

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    setFilter({
      ...filter,
      [key]: value,
    });
  };

  // 处理搜索
  const handleSearch = () => {
    setFilter(prev => ({
      ...prev,
      current: 1 // 重置到第一页
    }));
  };

  // 处理账户搜索
  const handleAccountSearch = _.debounce((val: string) => {
    if (!val) {
      setAccountOptions([]);
      return;
    }
    
    communityApi.recommendAccount_Search({ keyword: val })
      .then((res: any) => {
        setAccountOptions(res?.data?.list || []);
      });
  }, 500);

  // 处理交易账户搜索
  const handleTransactionSearch = _.debounce((val: string) => {
    if (!val) {
      setTransactionOptions([]);
      return;
    }
    
    communityApi.recommendAccount_Search({ keyword: val })
      .then((res: any) => {
        setTransactionOptions(res?.data?.list || []);
      });
  }, 500);

  // 处理账户选择
  const handleAccountSelect = (selected: any) => {
    if (!selected) {
      setAccountSearchValue(null);
      setFilter(prev => ({
        ...prev,
        account_id: undefined,
        keyword: ''
      }));
      return;
    }

    const selectedAccount = accountOptions.find(item => item.id === selected.key);
    if (selectedAccount) {
      setAccountSearchValue({
        label: `${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][selectedAccount.cert_type]}${selectedAccount.nick_name} | 小潮号：${selectedAccount.chao_id}`,
        value: selectedAccount.id
      });
      setFilter(prev => ({
        ...prev,
        account_id: selectedAccount.id,
        keyword: ''
      }));
    }
  };

  // 处理交易账户选择
  const handleTransactionSelect = (selected: any) => {
    if (!selected) {
      setTransactionSearchValue(null);
      setFilter(prev => ({
        ...prev,
        account_id: undefined,
        keyword: ''
      }));
      return;
    }

    const selectedAccount = transactionOptions.find(item => item.id === selected.key);
    if (selectedAccount) {
      setTransactionSearchValue({
        label: `${selectedAccount.nick_name} | 小潮号：${selectedAccount.chao_id}`,
        value: selectedAccount.id
      });
      setFilter(prev => ({
        ...prev,
        account_id: selectedAccount.id,
        keyword: ''
      }));
    }
  };

  // 处理标签页切换
  const handleTabChange = ({ target: { value } }: any) => {
    setActiveTab(value);
    // 重置搜索状态
    setAccountSearchValue(null);
    setTransactionSearchValue(null);
    setFilter(initialFilter);
  };

  // 获取账户列表数据
  const fetchAccountList = async () => {
    setLoading(true);
    try {
      const params = {
        size: filter.size,
        current: filter.current,
        ...(filter.account_type && { author_type: filter.account_type }),
        ...(filter.withdrawal_method && { binding: filter.withdrawal_method === '1' }),
        ...(filter.account_id && { account_id: filter.account_id }),
        ...(filter.sort_field && { sort_field: filter.sort_field }), // 使用sort_field字段
        ...(filter.sort_asc !== undefined && { order: filter.sort_asc === 1 ? 'asc' : 'desc' }),
      };
      
      const response = await communityApi.getEarningsAccountList(params);
      // 处理嵌套的 list 结构
      setAccountData(response.data.list);
    } finally {
      setLoading(false);
    }
  };

  // 获取收支记录列表
  const fetchTransactionList = async () => {
    setLoading(true);
    try {
      const params = {
        size: filter.size,
        current: filter.current,
        ...(filter.account_id && { account_id: filter.account_id }),
        ...(filter.biz_type && { biz_type: filter.biz_type }),
        ...(filter.fee_type && { fee_type: filter.fee_type }),
        ...(filter.begin && { begin: moment(filter.begin).format('YYYY-MM-DD HH:mm:ss') }),
        ...(filter.end && { end: moment(filter.end).format('YYYY-MM-DD HH:mm:ss') }),
      };
      
      // 使用communityApi.getEarningsFundFlowList替代不存在的getEarningsTransactionList
      const response = await communityApi.getEarningsFundFlowList(params);
      
      // 处理嵌套的 list 结构
      if (response?.data?.list) {
        const { list } = response.data;
        
        setTransactionData({
          total: list.total || 0,
          size: list.size || 10,
          pages: Math.ceil((list.total || 0) / (list.size || 10)),
          current: list.current || 1,
          records: list.records || [],
        });
      } else {
        // 如果API没有返回数据或发生错误，显示空数据
        setTransactionData({
          total: 0,
          size: 10,
          pages: 0,
          current: 1,
          records: [],
        });
      }
    } catch (error) {
      console.error('获取收支记录失败:', error);
      // 发生错误时显示空数据
      setTransactionData({
        total: 0,
        size: 10,
        pages: 0,
        current: 1,
        records: [],
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'account') {
      fetchAccountList();
    } else {
      fetchTransactionList();
    }
  }, [filter, activeTab]);

  // 处理排序
  const handleSort = (sort_asc: number, sort_by: number) => {
    const { sort_by: old_sort_by } = filter;
    
    if (sort_asc === -1) {
      // 当前选中其他排序的时候 另外一个的重置不生效
      if (old_sort_by !== sort_by) {
        return;
      }
      setFilter(prevFilter => ({
        ...prevFilter,
        sort_by: 0,
        sort_asc: -1,
      }));
      setPageInput(prevInput => ({
        ...prevInput,
        sort_by: 0,
        sort_asc: -1,
      }));
      return;
    }

    // 如果点击的是新列，则设置为升序
    // 根据排序字段设置对应的 sort_field
    const sort_field = sort_by === 1 ? 'unwithdrawn_balance' : 'withdrawn_balance';
    
    setFilter(prevFilter => ({
      ...prevFilter,
      sort_by,
      sort_asc,
      sort_field,
    }));
    setPageInput(prevInput => ({
      ...prevInput,
      sort_by,
      sort_asc,
      sort_field,
    }));
  };

  // 获取列配置 - 账户查询
  const getAccountColumns = (): ColumnProps<any>[] => {
    // 使用filter中的sort_by而不是pageInput中的
    const { sort_by, sort_asc } = filter;
    
    return [
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => (accountData.current - 1) * accountData.size + i + 1,
      width: 60,
    },
    {
      title: '账号昵称',
      dataIndex: 'nick_name',
      key: 'nick_name',
      width: 120,
      render: (text: string, record: any) => (
        <a onClick={() => showUserDetailModal(record)}>{text}</a>
      ),
    },
    {
      title: '账号类型',
      dataIndex: 'author_type_str',
      key: 'author_type_str',
      width: 100,
    },
    {
      title: '提现方式',
      dataIndex: 'alipay_user_no',
      key: 'alipay_user_no',
      width: 100,
      render: (text: string) => (text ? '已绑定' : '未绑定'),
    },
    {
      title: () => (
        <SortableColumn
          title="待提现金额"
          sort_by={sort_by}
          currentSortBy={1}
          pointerEvents={!filter.keyword}
          onChange={handleSort}
        />
      ),
      dataIndex: 'unwithdrawn_balance',
      key: 'unwithdrawn_balance',
      width: 120,
      render: (text: number) => {
        if (!text && text !== 0) return '-';
        return Number.isInteger(text) ? text : text.toFixed(2);
      },
    },
    {
      title: () => (
        <SortableColumn
          title="已提现金额"
            sort_by={sort_by}
          currentSortBy={2}
            pointerEvents={!filter.keyword}
          onChange={handleSort}
        />
      ),
      dataIndex: 'withdrawn_balance',
      key: 'withdrawn_balance',
      width: 120,
      render: (text: number) => {
        if (!text && text !== 0) return '-';
        return Number.isInteger(text) ? text : text.toFixed(2);
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 100,
      render: (text: any, record: any) => (
        <Link to={{
          pathname: `/view/feeRating/accountDetail/${record.account_id}`,
          state: { nick_name: record.nick_name }
        }}>查看明细</Link>
      ),
    },
  ];
  };

  // 组件加载后手动触发一次排序，确保默认排序生效
  useEffect(() => {
    // 如果已经是待提现金额降序，则不需要再次触发
    if (filter.sort_by === 1 && filter.sort_asc === 0) {
      return;
    }
    
    // 延迟执行，确保组件已经渲染
    const timer = setTimeout(() => {
      // 触发待提现金额降序排序
      handleSort(0, 1);
    }, 0);
    
    return () => clearTimeout(timer);
  }, []); // 只在组件首次加载时执行

  // 获取列配置 - 收支记录
  const getTransactionColumns = (): ColumnProps<TransactionRecord>[] => [
    {
      title: '账户',
      dataIndex: 'nick_name',
      key: 'nick_name',
      width: 120,
      render: (text: string, record: TransactionRecord) => (
        <a onClick={() => showUserDetailModal(record)}>{text}</a>
      ),
    },
    {
      title: '金额（元）',
      dataIndex: 'amount_str',
      key: 'amount_str',
      width: 120,
      render: (text: string) => {
        if (!text) return '-';
        // 如果第一个字符不是负号，则添加+号
        return text.charAt(0) !== '-' ? `+${text}` : text;
      },
    },
    {
      title: '事项',
      dataIndex: 'explain',
      key: 'explain',
      width: 150,
      render: (text: string, record: TransactionRecord) => {
        if (!text) return '-';
        // 根据flow_status显示对应状态文本
        let statusText = '';
        let statusColor = '';
        if (record.flow_status === 1) {
          statusText = '提现中';
          statusColor = ''; // 移除蓝色，使用默认色
        } else if (record.flow_status === 2) {
          statusText = '提现成功';
          statusColor = '#52c41a'; // 绿色
        } else if (record.flow_status === 3) {
          statusText = '提现失败';
          statusColor = '#f5222d'; // 红色
        }

        // 如果是上榜奖励类型，显示详按钮
        if (record.fee_type === 3) {
          return (
            <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <span>{text}</span>
              <Button
                type="dashed"
                size="small"
                style={{ padding: 3, paddingTop: 0, paddingBottom: 0 }}
                onClick={() => handleFeeDetail(record)}
              >
                详
              </Button>
              {statusText && (
                <span style={{ color: statusColor }}>({statusText})</span>
              )}
            </span>
          );
        }

        // 如果是提现成功或失败状态，整个事项可点击
        if (record.flow_status === 2 || record.flow_status === 3) {
          return (
            <span style={{ display: 'flex', alignItems: 'center', gap: 4, cursor: 'pointer' }} onClick={() => showWithdrawalDetail(record)}>
              <span>{text}</span>
              {statusText && (
                <span style={{ color: statusColor }}>({statusText})</span>
              )}
            </span>
          );
        }

        // 其他情况只显示事项和状态，不可点击
        return (
          <span>
            {text} {statusText && <span style={{ color: statusColor }}>({statusText})</span>}
          </span>
        );
      },
    },
    {
      title: '关联稿件',
      dataIndex: 'list_title',
      key: 'list_title',
      width: 200,
      render: (text: string, record: any) => {
        if (!text) return '-';
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <a
              onClick={() => {
                setPreview({
                  visible: true,
                  data: {
                    article_id: record.article_id,
                    doc_type: 10,
                  },
                  skey: Date.now(),
                });
              }}
            >
              {text}
              {record.mlf_id ? (
                <Button
                  type="dashed"
                  size="small"
                  style={{ padding: 3, paddingTop: 0, paddingBottom: 0, marginLeft: 5 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    showSourceInfo(record);
                  }}
                >
                  源
                </Button>
              ) : (
                ''
              )}
            </a>
          </div>
        );
      },
    },
    {
      title: '时间',
      dataIndex: 'flow_time_str',
      key: 'flow_time_str',
      width: 160,
    },
  ];

  // 获取搜索类型选项
  const getSearchTypeOptions = () => {
    if (activeTab === 'account') {
      return [
        <Select.Option key="account_id" value={1}>
          账户ID
        </Select.Option>,
        <Select.Option key="account_name" value={2}>
          账户名称
        </Select.Option>,
      ];
    }
    return [
      <Select.Option key="transaction_id" value={1}>
        交易ID
      </Select.Option>,
      <Select.Option key="account_id" value={2}>
        账户ID
      </Select.Option>,
    ];
  };

  // 处理表格排序
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const { field, order } = sorter;
    
    setFilter({
      ...filter,
      current: pagination.current,
      size: pagination.pageSize,
      // 如果没有新的排序字段，则保持当前的排序设置
      sort_field: field || filter.sort_field,
      order: order ? (order === 'ascend' ? 'asc' : 'desc') : (filter.sort_field ? 'desc' : undefined),
    });
  };

  // 导出确认弹窗内容
  const exportConfirmContent = (
    <div>
      如果当前列表数量超出上限，仅导出前 5000 行
    </div>
  );

  // 修改日期范围处理函数
  const handleDateRangeChange = (dates: any) => {
    setFilter(prev => ({
      ...prev,
      begin: dates?.[0],
      end: dates?.[1],
      current: 1, // 重置页码
    }));
  };

  // 显示源稿信息弹窗
  const showSourceInfo = (record: any) => {
    setSourceInfo({
      visible: true,
      sourceInfo: {
        ...record.ugc_article,
      },
    });
  };

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Radio.Group
            value={activeTab}
            onChange={handleTabChange}
            buttonStyle="solid"
            style={{ marginRight: 8 }}
          >
            <Radio.Button value="account">账户查询</Radio.Button>
            <Radio.Button value="transaction">收支记录</Radio.Button>
          </Radio.Group>
          <Button 
            onClick={() => {
              setMenuHook(dispatch, {
                ...props,
                location: {
                  pathname: '/view/feeRating/revenueQuery'
                }
              });
              history.push({
                pathname: '/view/earningsRule',
                state: {
                  breadCrumb: ['潮新闻', '创作收益管理', '收益查询', '创作收益说明']
                }
              });
            }}
            style={{ marginLeft: 8 }}
          >
            创作收益说明
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        {activeTab === 'account' && (
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={4}>
              <div style={{ 
                background: '#f8f9fa',
                padding: '12px 16px',
                borderRadius: '4px'
              }}>
                <div style={{ 
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '4px'
                }}>
                  <span style={{ fontSize: 14, color: '#666' }}>待提现金额</span>
                  <Tooltip title="全部账号的创作收益余额总和">
                    <Icon type="question-circle" style={{ marginLeft: 8, color: '#999' }} />
                  </Tooltip>
                </div>
                <div style={{ 
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#262626'
                }}>
                  {earningsTotal.withdrawable_total.toFixed(2)}
                </div>
              </div>
            </Col>
            <Col span={4}>
              <div style={{ 
                background: '#f8f9fa',
                padding: '12px 16px',
                borderRadius: '4px'
              }}>
                <div style={{ 
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '4px'
                }}>
                  <span style={{ fontSize: 14, color: '#666' }}>已提现金额</span>
                  <Tooltip title="全部账号的成功提现金额总和，不包括提现中及提现失败的部分">
                    <Icon type="question-circle" style={{ marginLeft: 8, color: '#999' }} />
                  </Tooltip>
                </div>
                <div style={{ 
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#262626'
                }}>
                  {earningsTotal.withdrawn_total.toFixed(2)}
                </div>
              </div>
            </Col>
          </Row>
        )}

        {/* 筛选工具栏 - 账户查询 */}
        {activeTab === 'account' && (
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item style={{ marginRight: 4 }}>
                <PermButton perm="earnings_account:export" type="primary" onClick={handleExport}>
                  <Icon type="download" /> 导出数据
                </PermButton>
              </Form.Item>
              <Form.Item style={{ marginRight: 4 }}>
                <Select
                  value={filter.account_type}
                  onChange={(value) => handleFilterChange('account_type', value)}
                  style={{ width: 120, marginRight: 4 }}
                  placeholder="账号类型"
                >
                  <Select.Option value="">账户类型</Select.Option>
                  <Select.Option value="2">潮鸣号</Select.Option>
                  <Select.Option value="1">潮客</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item style={{ marginRight: 4 }}>
                <Select
                  value={filter.withdrawal_method}
                  onChange={(value) => handleFilterChange('withdrawal_method', value)}
                  style={{ width: 120, marginRight: 4 }}
                  placeholder="提现方式"
                >
                  <Select.Option value="">提现方式</Select.Option>
                  <Select.Option value="1">已绑定</Select.Option>
                  <Select.Option value="2">未绑定</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Select
                value={activeTab === 'account' 
                  ? (accountSearchValue ? {
                      key: accountSearchValue.value,
                      label: accountSearchValue.label
                    } : undefined)
                  : (transactionSearchValue ? {
                      key: transactionSearchValue.value,
                      label: transactionSearchValue.label
                    } : undefined)
                }
                labelInValue
                placeholder="输入昵称或小潮号搜索"
                onSearch={activeTab === 'account' ? handleAccountSearch : handleTransactionSearch}
                onChange={activeTab === 'account' ? handleAccountSelect : handleTransactionSelect}
                style={{ width: 200, marginRight: 8 }}
                showSearch
                allowClear
                filterOption={false}
              >
                {(activeTab === 'account' ? accountOptions : transactionOptions).map((d: any) => (
                  <Select.Option
                    style={{
                      whiteSpace: 'pre-wrap',
                    }}
                    key={d.id}
                    value={d.id}
                  >
                    {activeTab === 'account' 
                      ? `${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type]}${d.nick_name} | 小潮号：${d.chao_id}`
                      : `${d.nick_name} | 小潮号：${d.chao_id}`
                    }
                  </Select.Option>
                ))}
              </Select>
              <Button type="primary" onClick={handleSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>
        )}

        {/* 筛选工具栏 - 收支记录 */}
        {activeTab === 'transaction' && (
          <Row style={{ marginBottom: 16 }}>
            <Col span={24}>
              <div style={{ display: 'flex', flexWrap: 'nowrap', alignItems: 'center' }}>
                <PermButton perm="earnings_fund_flow:export_fund_flow" type="primary" onClick={handleExport} style={{ marginRight: 8, flexShrink: 0 }}>
                  <Icon type="download" /> 导出数据
                </PermButton>
                <Select
                  value={filter.biz_type}
                  onChange={(value) => handleFilterChange('biz_type', value)}
                  style={{ width: 120, marginRight: 8, flexShrink: 0 }}
                  placeholder="按收支筛选"
                >
                  <Select.Option value="">按收支筛选</Select.Option>
                  <Select.Option value="1">收益</Select.Option>
                  <Select.Option value="2">提现</Select.Option>
                  <Select.Option value="3">过期</Select.Option>
                </Select>
                <Select
                  value={filter.fee_type}
                  onChange={(value) => handleFilterChange('fee_type', value)}
                  style={{ width: 120, marginRight: 8, flexShrink: 0 }}
                  placeholder="收益类型"
                >
                  <Select.Option value="">收益类型</Select.Option>
                  <Select.Option value="1">取稿稿费</Select.Option>
                  <Select.Option value="2">指定稿费</Select.Option>
                  <Select.Option value="3">上榜奖励</Select.Option>
                </Select>
                <DatePicker.RangePicker
                  value={[filter.begin, filter.end]}
                  onChange={handleDateRangeChange}
                  style={{ marginRight: 8, flexShrink: 0 }}
                  showTime
                />
                <div style={{ marginLeft: 'auto' }}>
                  <Select
                    value={transactionSearchValue ? {
                      key: transactionSearchValue.value,
                      label: transactionSearchValue.label
                    } : undefined}
                    labelInValue
                    placeholder="输入昵称或小潮号搜索"
                    onSearch={handleTransactionSearch}
                    onChange={handleTransactionSelect}
                    style={{ width: 200, marginRight: 8 }}
                    showSearch
                    allowClear
                    filterOption={false}
                  >
                    {transactionOptions.map((d: any) => (
                      <Select.Option
                        style={{
                          whiteSpace: 'pre-wrap',
                        }}
                        key={d.id}
                        value={d.id}
                      >
                        {`${d.nick_name} | 小潮号：${d.chao_id}`}
                      </Select.Option>
                    ))}
                  </Select>
                  <Button type="primary" onClick={handleSearch}>
                    <Icon type="search" /> 搜索
                  </Button>
                </div>
              </div>
            </Col>
          </Row>
        )}

        <Table
          loading={loading}
          rowKey="id"
          dataSource={activeTab === 'account' ? accountData.records : transactionData.records}
          columns={activeTab === 'account' ? getAccountColumns() : getTransactionColumns()}
          pagination={false}
          scroll={{ x: 1200 }}
        />

        <Row style={{ marginTop: 16 }}>
          <Col span={12}>
            <div style={{ lineHeight: '32px' }}>
              共 {activeTab === 'account' ? accountData.total : transactionData.total} 条数据
            </div>
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            <Pagination
              total={activeTab === 'account' ? accountData.total : transactionData.total}
              pageSize={filter.size}
              current={filter.current}
              showSizeChanger
              showQuickJumper
              pageSizeOptions={['10', '20', '50', '100']}
              onChange={(page, pageSize) => handleTableChange({ current: page, pageSize }, {}, { field: filter.sort_field, order: filter.order === 'asc' ? 'ascend' : 'descend' })}
              onShowSizeChange={(current, size) => handleTableChange({ current: 1, pageSize: size }, {}, { field: filter.sort_field, order: filter.order === 'asc' ? 'ascend' : 'descend' })}
            />
          </Col>
        </Row>
      </div>

      {/* 用户详情弹窗 */}
      <Modal
        visible={userDetailModal.visible}
        key={userDetailModal.key}
        title="用户详情"
        width={800}
        onCancel={() => setUserDetailModal({ ...userDetailModal, visible: false })}
        onOk={() => setUserDetailModal({ ...userDetailModal, visible: false })}
      >
        {userDetailModal.visible && <UserDetail detail={userDetailModal.detail} />}
      </Modal>

      {/* 提现详情弹窗 */}
      <Modal
        visible={withdrawalDetailModal.visible}
        title="提现详情"
        onCancel={closeWithdrawalDetail}
        onOk={closeWithdrawalDetail}
        width={400}
        footer={[
          <Button key="close" onClick={closeWithdrawalDetail}>
            关闭
          </Button>,
        ]}
      >
        {withdrawalDetailModal.record && (
          <div>
            <p>提现方式：{withdrawalDetailModal.record.withdraw_method}</p>
            <p>支付宝姓名：{withdrawalDetailModal.record.cert_name}</p>
            <p>支付宝账号：{withdrawalDetailModal.record.alipay_user_no}</p>
            {withdrawalDetailModal.record.flow_status === 2 ? (
                <>
                <p>支付宝交易号：{withdrawalDetailModal.record.order_id}</p>
                <p>到账时间：{withdrawalDetailModal.record.flow_time_str}</p>
                </>
              ) : (
                <>
                <p>失败原因：{withdrawalDetailModal.record.description}</p>
                <p>失败时间：{withdrawalDetailModal.record.flow_time_str}</p>
                </>
              )}
          </div>
        )}
      </Modal>

      {/* 上榜详情弹窗 */}
      <Modal
        visible={rankDetailModal.visible}
        title="上榜详情"
        width={600}
        onCancel={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
        footer={[
          <Button
            key="ok"
            type="primary"
            onClick={() => setRankDetailModal({ ...rankDetailModal, visible: false })}
          >
            确定
          </Button>,
        ]}
        destroyOnClose={true}
      >
        {rankDetailModal.loading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Icon type="loading" />
          </div>
        ) : (
          <div style={{ padding: '20px 0' }}>
            {rankDetailModal.data?.rank_name ? (
              <div>
                <a
                  href={rankDetailModal.data?.rank_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: '#1890ff', textDecoration: 'none', fontSize: '14px' }}
                >
                  {rankDetailModal.data.rank_name}
                </a>
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                暂无上榜详情
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 预览稿件弹窗 */}
      <PreviewMCN
        visible={preview.visible}
        skey={preview.skey}
        data={preview.data}
        onClose={() => setPreview({ ...preview, visible: false })}
      />

      {/* 源稿信息弹窗 */}
      <SourceInfoModal
        visible={sourceInfo.visible}
        sourceInfo={sourceInfo.sourceInfo}
        onCancel={() => setSourceInfo({ ...sourceInfo, visible: false })}
      />
    </>
  );
} 