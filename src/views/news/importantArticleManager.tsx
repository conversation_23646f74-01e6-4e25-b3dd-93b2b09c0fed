import { releaseListApi as api } from '@app/api';
import {
  IResTopNewsListAllData,
  IResTopNewsListRecordsData,
  ISysChannel,
  ITableProps,
  IBaseProps,
  CommonObject,
} from '@app/types';
import { A, Table, BaseComponent, SearchAndInput, PreviewMCN } from '@components/common';
import { connectTable as connect } from '@utils/connect';
import { getCrumb } from '@utils/utils';
import { Button, Col, Icon, message, Modal, Row, Form, Tooltip } from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';

type State = {
  channelId: string;
  channel: ISysChannel;
  preview: {
    visible: boolean;
    skey: number;
    data: CommonObject;
  };
  form: {
    visible: boolean;
    key: number;
    selectedList: number[];
    error: boolean;
  };
  loading: boolean;
  ids: number[];
};

type Props = IBaseProps<
  ITableProps<IResTopNewsListRecordsData, IResTopNewsListAllData>,
  { id: string }
>;

class ImportantArticleManager extends BaseComponent<
  ITableProps<IResTopNewsListRecordsData, IResTopNewsListAllData>,
  State,
  { id: string }
> {
  constructor(props: Props) {
    super(props);
    this.state = {
      channelId: props.match.params.id,
      channel: {
        category_id: 1,
        focus_carousel: false,
        id: '1',
        mode: 0,
        name: '',
        tou_tiao: false,
      },
      form: {
        selectedList: [],
        visible: false,
        key: Date.now(),
        error: false,
      },
      preview: {
        visible: false,
        skey: Date.now() + 1,
        data: {},
      },
      loading: false,
      ids: [],
    };
  }

  componentDidMount() {
    this.getData();
  }

  componentDidUpdate(prevProps: Props) {
    if (
      prevProps.tableList.timestamp !== this.props.tableList.timestamp &&
      Boolean(this.props.tableList.allData.article_list)
    ) {
      this.setState({
        ids: this.props.tableList.allData.article_list.map((v: IResTopNewsListRecordsData) => v.id),
      });
    }
  }

  getData = () => {
    this.dispatchTable('getImportantArticlesList', 'article_list', {
      channel_id: this.state.channelId,
    });
  };

  getColumns = () => {
    const { total } = this.props.tableList;
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, ercord: any, i: number) => <span>{i + 1}</span>,
        width: 70,
      },
      {
        title: '潮新闻ID',
        key: 'scid',
        dataIndex: 'id',
        width: 100,
      },
      // {
      //   title: '创作者平台ID',
      //   key: 'metadata_id',
      //   dataIndex: 'metadata_id',
      //   width: 115,
      // },
      {
        title: '标题',
        key: 'list_title',
        dataIndex: 'list_title',
        render: (text: any, record: any) => (
          <a onClick={this.preview.bind(this, record)} className="list-title">
            {(text.length > 30 ? `${text.slice(0, 30)}...` : text) || '-'}
          </a>
        ),
      },
      {
        title: '封面图/配图',
        key: 'list_pics',
        dataIndex: 'list_pics',
        width: 150,
        align: 'center',
        render: (text: any, record: any) =>
          text ? (
            <div style={{ height: 60, textAlign: 'center' }}>
              <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
            </div>
          ) : (
            <></>
          ),
      },
      {
        title: '类型',
        key: 'doc_type',
        dataIndex: 'doc_type',
        width: 100,
        render: (text: any, record: any) => {
          return ['小视频', '', '短图文'][text - 10];
        },
      },
      // {
      //   title: '发稿人',
      //   key: 'creator',
      //   dataIndex: 'creator',
      //   width: 90,
      // },
      {
        title: '作者',
        key: 'author',
        dataIndex: 'author',
        width: 110,
      },
      {
        title: '发布时间',
        key: 'published_at',
        dataIndex: 'published_at',
        render: (text: any, record: any) => (
          <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        ),
        width: 95,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) =>
          this.requirePerm(`articles_top:${this.state.channelId}:1:cancel`)(
            <A onClick={this.cancelTop.bind(this, record.article_top_id)}>取消推荐</A>
          ),
        width: 90,
      },
    ];
  };

  exchangeOrder = (id: string, sortFlag: number) => {
    this.setLoading(true);
    api
      .updateImportantArticleSort({ id, sort_flag: sortFlag })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.setLoading(false);
      })
      .catch(() => {
        this.setLoading(false);
      });
  };

  preview = (record: any) => {
    this.setState({
      preview: {
        visible: true,
        skey: Date.now(),
        data: record,
      },
    });
  };

  cancelTop = (id: string) => {
    Modal.confirm({
      title: '是否确定要将该新闻取消重点推荐',
      onOk: () => {
        this.setLoading(true);
        api
          .cancelImportantArticle({ id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setLoading(false);
          })
          .catch(() => {
            this.setLoading(false);
          });
      },
    });
  };

  createArticle = () => {
    if (this.props.tableList.total >= 20) {
      message.error('最多只能添加20条，请先取消后再添加');
      return;
    }
    this.setState({
      form: {
        selectedList: [],
        visible: true,
        key: Date.now(),
        error: false,
      },
    });
  };

  getSearchColumn = () => {
    return [
      {
        title: '新闻频道',
        key: 'channel',
        dataIndex: 'channel_name',
        width: 95,
      },
      {
        title: '新闻标题',
        key: 'list_title',
        dataIndex: 'list_title',
      },
      {
        title: '类型',
        key: 'doc_type',
        dataIndex: 'doc_type',
        width: 80,
        render: (text: any) => {
          return ['小视频', '', '短图文'][text - 10];
        },
      },
    ];
  };

  handleSubmitAdd = () => {
    if (
      this.state.form.selectedList.length === 0 ||
      this.state.form.selectedList.length > 20 - this.props.tableList.total
    ) {
      this.setState({ form: { ...this.state.form, error: true } });
      message.error('请检查表单项');
      return;
    }
    this.setState({ loading: true });
    api
      .createImportantArticle({
        channel_id: this.state.channelId,
        article_id: this.state.form.selectedList.join(','),
      })
      .then(() => {
        this.getData();
        this.setState({ form: { ...this.state.form, visible: false }, loading: false });
      })
      .catch(() => {
        this.setState({ loading: false });
      });
  };

  handleFormChange = (value: any) => {
    this.setState({ form: { ...this.state.form, selectedList: value } });
  };

  backToList = () => {
    this.props.history.go(-1);
    // this.props.history.push(`/view/chaoke?channel_id=${this.state.channelId}`);
  };

  closePreview = () => {
    this.setState({
      preview: { ...this.state.preview, visible: false },
    });
  };

  render() {
    const bread = ['用户内容管理', '潮客内容管理', '重点推荐管理'];
    const { form, loading, preview } = this.state;
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button style={{ marginRight: 8 }} onClick={this.backToList}>
              <Icon type="left-circle-o" /> 返回
            </Button>
            {this.requirePerm(`articles_top:${this.state.channelId}:1:save`)(
              <Button style={{ marginRight: 8 }} onClick={this.createArticle}>
                <Icon type="plus-circle-o" /> 添加推荐作品
              </Button>
            )}
            <Tooltip title="可配置最多20条UGC小视频或短图文（需带图片），潮客/潮鸣号发布的均可">
              <Icon type="question-circle" />
            </Tooltip>
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(bread)}
          </Col>
        </Row>
        <div className="component-content">
          <Table
            func="getImportantArticlesList"
            pagination={false}
            index="article_list"
            columns={this.getColumns()}
            filter={{ channel_id: this.state.channelId }}
            rowKey="id"
          />
          <Modal
            visible={form.visible}
            key={form.key}
            confirmLoading={loading}
            onCancel={() => this.setState({ form: { ...form, visible: false } })}
            onOk={this.handleSubmitAdd}
            width={600}
            title="添加重点推荐作品"
          >
            <Form.Item
              label={null}
              validateStatus={form.error ? 'error' : ''}
              help={form.error ? `请选择新闻，最多${20 - this.props.tableList.total}条` : ''}
            >
              <SearchAndInput
                excludeIds={this.state.ids}
                max={20 - this.props.tableList.total}
                func="listArticleRecommendSearch"
                columns={this.getSearchColumn()}
                onChange={this.handleFormChange}
                body={{ doc_types: '10,12' }}
                placeholder="输入小视频/短图文ID或标题"
                idField="id"
                onSelected={(value) => {
                  if (value.doc_type == 12 && !value.list_pics) {
                    return '只支持添加带有图片的短图文作品'
                  }
                  return ''
                }}
              />
            </Form.Item>
          </Modal>
          <PreviewMCN {...preview} onClose={this.closePreview} />
        </div>
      </>
    );
  }
}

export default withRouter(
  connect<IResTopNewsListRecordsData, IResTopNewsListAllData>()(ImportantArticleManager)
);
