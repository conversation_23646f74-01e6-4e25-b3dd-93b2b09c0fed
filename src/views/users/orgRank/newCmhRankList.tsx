import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Table, A, OrderColumn } from '@components/common';
import { getCrumb, objectToQueryString, searchToObject, setMenuHook } from '@app/utils/utils';
import {
  Row,
  Col,
  Input,
  Divider,
  Switch,
  message,
  Icon,
  Modal,
  Form,
  Button,
  DatePicker,
  Select,
  TimePicker,
  Radio,
} from 'antd';
import { getTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import { setConfig } from '@app/action/config';
import moment from 'moment';
import ExponentConfigDrawer from './exponentConfigDrawer';
import UpdateRankModal from './updateRankModal';
import SendRewardsDrawer from './sendRewards'; // ✅ 新增：导入上榜奖励抽屉组件
import RankPublishConfigModal from './rankPublishConfigModal';
import ManualPublishingModal from './manualPublishingModal';

interface RewardModal {
  visible: boolean;
  summary_month?: string;
}

export default function NewCmhRankList(props: any) {
  const {
    total,
    allData: {
      publish_switch = 0,
      publish_time = '',
      pusblish_day = '1',

      week_publish_time = '',
      week_publish_day = '1',
    },
  } = useSelector((state: any) => state.tableList);

  const dispatch = useDispatch();
  const store = useStore();
  const history = useHistory();
  const { loading, run } = useXHR();

  const [filter, setFilter] = useState<any>({
    category: parseInt(searchToObject().category ?? 0),
  });

  const [rank_dates_week, setRankDateWeek] = useState<any[]>([]);

  const [changeNameModal, setChangeNameModal] = useState<any>({
    visible: false,
    key: 'change-name-modal',
  });

  const [exponentConfigDrawer, setExponentConfigDrawer] = useState<any>({
    visible: false,
  });

  const [updateDateModal, setUpdateDateModal] = useState<any>({
    visible: false,
    beginDate: undefined,
    key: 'update-date-modal',
  });

  const [publishModal, setPublishModal] = useState<any>({
    key: Date.now() + 1,
    visible: false,
    publish_switch: true,
    publish_time: undefined,
    day: '1',
    week_publish_day: '1',
    week_publish_time: undefined,
  });

  const [manualPublishingModal, setManualPublishingModal] = useState<any>({
    visible: false,
    key: Date.now() + 2,
  });

  const [rewardModal, setRewardModal] = useState<RewardModal>({
    visible: false,
  });

  const getList = () => {
    dispatch(getTableList('getCmhRankList', 'page', { ...filter }));
  };

  const onChangeType = (v: any, name: any) => {
    let { pathname: path, search } = history.location;
    const obj = searchToObject();
    obj[name] = v;
    path = `${path}?${objectToQueryString(obj)}`;
    history.replace(path);
    setFilter({
      ...filter,
      [name]: v,
    });
  };

  const editRecord = (type: 'name' | 'rule', record: any) => {
    setChangeNameModal({
      visible: true,
      key: 'change-name-modal',
      type,
      id: record.id,
      name: record.type,
    });
  };

  const handleSort = (record: any, sort_type: 1 | 2) => {
    run(api.updateCmhRankSort, { id: record.id, sort_type, category: filter.category }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const updateStatus = (record: any) => {
    run(api.updateCmhRankStatus, { id: record.id, status: record.status == 1 ? 2 : 1 }, true).then(
      () => {
        message.success('操作成功');
        getList();
      }
    );
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      render: (text: any, record: any, i: number) => (
        <OrderColumn
          perm="ranks_cmh:sort"
          pos={i}
          start={0}
          end={total - 1}
          onUp={() => handleSort(record, 1)}
          onDown={() => handleSort(record, 2)}
        />
      ),
      width: 70,
    },
    {
      title: '序号',
      key: 'seq',
      render: (text: any, record: any, i: number) => i + 1,
      width: 70,
    },
    {
      title: '榜单名称',
      dataIndex: 'name',
      render: (text: any, record: any) => (
        <PermA
          perm="ranks_cmh:result_data"
          onClick={() =>
            history.push(
              `/view/singleCmhRank/${record.id}/${record.rank_type}/${
                filter.category
              }/${encodeURIComponent(record.name)}`
            )
          }
        >
          {text}
        </PermA>
      ),
    },
    {
      title: '前台展示榜单名称',
      dataIndex: 'type',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: any) => (text == 1 ? '展示中' : '不展示'),
      width: 80,
    },
    {
      title: '操作',
      key: 'op',
      render: (record: any) => (
        <span>
          <PermA perm="ranks_cmh:online" onClick={() => updateStatus(record)}>
            {record.status == 1 ? '下架' : '上架'}
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="ranks_cmh:edit"
            onClick={() => editRecord('name', record)}
            style={{ marginLeft: 8 }}
          >
            编辑名称
          </PermA>
          <Divider type="vertical" />
          <PermA
            perm="ranks_cmh:result_data"
            onClick={() =>
              history.push(
                `/view/singleCmhRank/${record.id}/${record.rank_type}/${
                  filter.category
                }/${encodeURIComponent(record.name)}`
              )
            }
          >
            榜单数据
          </PermA>
        </span>
      ),
      width: 220,
    },
  ];

  useEffect(() => {
    setMenuHook(dispatch, props);

    handleGetMonthsWeek();
  }, []);

  useEffect(() => {
    getList();
  }, [filter]);

  const handleChangeName = () => {
    if (!changeNameModal.name?.trim()) {
      message.error('请填写名称');
      return;
    }
    const body: any = { id: changeNameModal.id, type: changeNameModal.name.trim() };

    run(api.updateCmhRank, body).then(() => {
      message.success('操作成功');
      setChangeNameModal({ visible: false });
      getList();
    });
  };

  const handleReward = () => {
    // setRewardModal({ visible: false });
  };

  const handleGetMonthsWeek = async () => {
    try {
      const res: any = await userApi.getCmhRankWeekList({});
      const list = res?.data?.list || [];
      setRankDateWeek(list);
    } catch (error) {}
  };

  // 计算上个月
  const lastMonth = moment().subtract(1, 'months').format('YYYYMM');
  const lastMonthDisplay = moment().subtract(1, 'months').format('YYYY-MM');

  // 每次打开奖励弹窗时自动设置 summary_month
  useEffect(() => {
    if (rewardModal.visible) {
      setRewardModal((prev: any) => ({ ...prev, summary_month: lastMonth }));
    }
    // eslint-disable-next-line
  }, [rewardModal.visible]);

  return (
    <>
      <Row className="layout-infobar">
        <Col span={16} style={{ display: 'flex', overflow: 'auto' }}>
          <PermButton perm="" onClick={() => history.push(`/view/cmhRankClassMgr`)}>
            参评主体管理
          </PermButton>
          <PermButton
            perm="ranks_cmh:config"
            style={{ marginLeft: 8 }}
            onClick={() => setExponentConfigDrawer({ visible: true })}
          >
            指数配置
          </PermButton>
          <PermButton
            perm="ranks_cmh:publish"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setPublishModal({
                key: Date.now() + 1,
                visible: true,
                month_publish_time: !!publish_time ? moment(publish_time, 'HH:mm') : undefined,
                month_publish_day: pusblish_day,

                week_publish_time: !!week_publish_time
                  ? moment(week_publish_time, 'HH:mm')
                  : undefined,
                week_publish_day: week_publish_day,
                publish_switch: Boolean(publish_switch),
              })
            }
          >
            发榜设置
          </PermButton>
          <PermButton
            perm="ranks_cmh:update_data"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setUpdateDateModal({
                visible: true,
                key: 'update-date-modal',
                dates: rank_dates_week,
              })
            }
          >
            更新榜单数据
          </PermButton>
          <PermButton
            perm="ranks_cmh:publish"
            style={{ marginLeft: 8 }}
            onClick={() =>
              setManualPublishingModal({
                visible: true,
                rankType: filter.category,
                key: Date.now() + 2,
              })
            }
          >
            手动发榜
          </PermButton>
          <PermButton
            perm=""
            style={{ marginLeft: 8 }}
            onClick={() =>
              setRewardModal({
                visible: true,
              })
            }
          >
            上榜奖励
          </PermButton>
        </Col>
        <Col span={8} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 10 }}>
          <Radio.Group
            value={filter.category}
            buttonStyle="solid"
            onChange={(e) => onChangeType(e.target.value, 'category')}
          >
            <Radio.Button value={1}>周榜</Radio.Button>
            <Radio.Button value={0}>月榜</Radio.Button>
          </Radio.Group>
        </Row>
        <Table
          func="getCmhRankList"
          index="page"
          filter={filter}
          columns={columns}
          rowKey="id"
          pagination={false}
        />
        <Modal
          visible={changeNameModal.visible}
          title={'编辑榜单'}
          onCancel={() => setChangeNameModal({ visible: false })}
          onOk={handleChangeName}
          confirmLoading={loading}
          key={`change-name-modal-${changeNameModal.id || ''}`}
        >
          <Form labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
            <Form.Item label="榜单名称" required>
              <Input
                maxLength={15}
                value={changeNameModal.name}
                onChange={(e: any) =>
                  setChangeNameModal({ ...changeNameModal, name: e.target.value })
                }
                placeholder="请输入榜单名称"
              />
            </Form.Item>
          </Form>
        </Modal>

        <UpdateRankModal
          {...updateDateModal}
          onCancel={() => setUpdateDateModal({ visible: false, key: 'update-date-modal' })}
          onEnd={() => {
            setUpdateDateModal({ visible: false, key: 'update-date-modal' });
          }}
        ></UpdateRankModal>

        <RankPublishConfigModal
          {...publishModal}
          onOk={() => {
            setPublishModal({ visible: false });
            getList();
          }}
          onCancel={() => setPublishModal({ visible: false })}
        ></RankPublishConfigModal>

        <ManualPublishingModal
          {...manualPublishingModal}
          type="cmh"
          weeks={rank_dates_week}
          onOk={() => {
            setManualPublishingModal({ visible: false });
            getList();
          }}
          onCancel={() => setManualPublishingModal({ visible: false })}
        ></ManualPublishingModal>

        <SendRewardsDrawer
          {...rewardModal}
          onClose={() => setRewardModal({ visible: false })}
          onOk={handleReward}
          rankType="cmh"
        />

        <ExponentConfigDrawer
          {...exponentConfigDrawer}
          onClose={() => setExponentConfigDrawer({ visible: false })}
          onOk={() => setExponentConfigDrawer({ visible: false })}
        ></ExponentConfigDrawer>
      </div>
    </>
  );
}
