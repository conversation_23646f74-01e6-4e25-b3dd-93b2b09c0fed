/* eslint-disable no-nested-ternary */
declare const BUILD_ENV: 'dev' | 'test' | 'prev' | 'prod' | 'testb';

import React, { useState, useEffect, useMemo, useReducer } from 'react';
import { useDispatch, useStore, useSelector } from 'react-redux';
import { useHistory, useParams, useRouteMatch } from 'react-router-dom';
import { A, SearchAndInput } from '@components/common';
import { getCrumb, showCommonIDDetailModal } from '@app/utils/utils';
import ReactClipboard from 'react-clipboardjs-copy';
import {
  Row,
  Col,
  Button,
  message,
  Icon,
  Modal,
  Form,
  Select,
  Tooltip,
  InputNumber,
  Radio,
  Input,
} from 'antd';
import { getTableList, clearTableList } from '@app/action/tableList';
import { userApi as api, userApi } from '@app/api';
import useXHR from '@utils/useXhr';
import { PermA, PermButton } from '@app/components/permItems';
import Table from '@app/components/common/table';
import RankInstructionsModal from './rankInstructionsModal';
import moment from 'moment';
import { setConfig } from '@app/action/config';

const format = (date: string) => {
  if (!date) return date;
  return moment(date, 'YYYYMMDD').format('YYYY.MM.DD');
};

export default function SingleCmhRank(props: any) {
  const [search, setSearch] = useState<any>({
    keyword: '',
    type: 1,
  });

  const dispatch = useDispatch();
  const { id, rank_type, rank_name, category } = useParams<any>();
  const history = useHistory();
  const { loading, run } = useXHR();
  const {
    allData: { publish, rank, columns },
    total,
    current,
    size,
    records = [],
  } = useSelector((state: any) => state.tableList);

  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  const [rankInstructionsModal, setRankInstructionsModal] = useState<any>({
    visible: false,
  });

  const [showCountModal, setShowCountModal] = useState<any>({
    visible: false,
    count: 0,
  });

  const [rank_dates, setRankDate] = useState<any>([]);
  const [rank_dates_week, setRankDateWeek] = useState<any>([]);
  const [rankCategorys, setRankCategorys] = useState<any>([]);
  const [filter, setFilter] = useState<any>({
    type_value: '',
    keyword: '',
    // summary_month: '',
    // summary_year: '',
    // summary_week: '',
    rank_id: id,
    hidden: '',
  });
  const [rank_status, setRankStatus] = useState<any>(false);
  const { allData } = useSelector((state: any) => state.tableList);
  useEffect(() => {
    setRankStatus(allData?.publish?.status === 1 && allData?.publish?.is_show === 1);
  }, [allData]);
  useEffect(() => {
    if (category == 0) {
      handleGetMonths();
    } else {
      handleGetMonthsWeek();
    }
  }, []);

  useEffect(() => {
    if ((category == 0 && !!filter.summary_month) || (category == 1 && !!filter.summary_week)) {
      getList(true);
    }
  }, [filter]);

  const getList = (goToFirstPage = false, newFilter = filter) => {
    let cur = goToFirstPage ? 1 : current;
    dispatch(
      getTableList(rank_type == 4 ? 'getSingleCmhArticleList' : 'getSingleCmhRankList', 'page', {
        current: cur,
        size,
        ...newFilter,
      })
    );
  };

  const editTitle = (record: any) => {
    let title: string = record.new_title || '';
    let modal: any;
    const titleChange = (e: any) => {
      title = e.target.value;
      modal.update({
        content: (
          <>
            <Input.TextArea
              placeholder="最多输入40字"
              value={title}
              maxLength={40}
              onPressEnter={(e) => e.preventDefault()}
              onChange={titleChange}
            ></Input.TextArea>
            <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '自定义标题',
      content: (
        <>
          <Input.TextArea
            placeholder="最多输入40字"
            value={title}
            maxLength={40}
            onPressEnter={(e) => e.preventDefault()}
            onChange={titleChange}
          ></Input.TextArea>
          <div style={{ textAlign: 'right' }}>{title?.length}/40</div>
        </>
      ),
      onOk: (destroy: Function) => {
        // if (!title?.trim()) {
        //   message.error('请输入自定义标题');
        //   return;
        // }
        const params: any = {
          rank_id: id,
          new_title: title?.trim(),
          article_id: record.article_id,
        };
        if (category == 1) {
          params.summary_year = publish.summary_year;
          params.summary_week = publish.summary_week;
        } else {
          params.summary_month = publish.summary_month;
        }

        api.updateCmhRankTitle(params).then((res: any) => {
          message.success('编辑成功');
          getList();
        });

        destroy();
      },
    });
  };

  const getSeq = (i: number) => (current - 1) * size + i + 1;
  const getColumns = () => {
    let result = [
      {
        title: '序号',
        key: 'id',
        render: (a: any, b: any, c: number) => getSeq(c),
      },
      {
        title: '潮鸣号',
        key: 'cmh_name',
        dataIndex: 'cmh_name',
      },
      ...(rank_type == 5
        ? [
            {
              title: '行业类型',
              key: 'industry',
              dataIndex: 'industry',
            },
          ]
        : []),
      ...(rank_type == 2 || rank_type == 3
        ? [
            {
              title: '地市',
              key: 'location',
              dataIndex: 'location',
            },
          ]
        : []),
      {
        title: '指数',
        key: 'index_number',
        dataIndex: 'index_number',
      },
      ...(columns || []).map((v: any, i: any) => ({
        title: `${v.name}得分`,
        render: (a: any, b: any) => Number(((b.details[i] || {}).rvalue || 0).toFixed(2)),
      })),
      {
        title: '健康度得分',
        key: 'operation_value',
        dataIndex: 'operation_value',
        width: 130,
        render: (a: any, b: any, index: number) =>
          b.isPointEdit ? (
            <>
              <InputNumber
                placeholder="输入正负数字"
                value={b.new_operation_value}
                onChange={(e) => {
                  b.new_operation_value = e;
                  forceUpdate();
                }}
              />
            </>
          ) : (
            Number((a || 0).toFixed(2))
          ),
      },
      {
        title: '操作',
        width: 120,
        render: (_: any, a: any, b: number) => (
          <span>
            {!a.isPointEdit ? (
              <PermA perm="ranks_cmh:update_data" onClick={() => toggleEdit(b, true)}>
                编辑
              </PermA>
            ) : (
              <>
                <PermA
                  perm=""
                  onClick={() => {
                    handleModifyRankData(b);
                  }}
                  style={{ marginRight: 8 }}
                >
                  保存
                </PermA>
                <PermA perm="" onClick={() => toggleEdit(b, false)}>
                  取消
                </PermA>
              </>
            )}
          </span>
        ),
      },
    ];
    if (rank_type == 4) {
      result = [
        {
          title: '序号',
          key: 'seq',
          render: (a: any, b: any, c: number) => getSeq(c),
          width: 80,
        },
        {
          width: 80,
          title: '稿件ID',
          key: 'article_id',
          dataIndex: 'article_id',
          render: (text: any, record: any) => (
            <a
              onClick={() => {
                showCommonIDDetailModal([
                  { key: '潮新闻ID', value: record.article_id },
                  { key: 'uuid', value: record.uuid },
                  { key: '创作者平台ID', value: record.creator_id },
                ]);
              }}
            >
              {text}
            </a>
          ),
        },
        {
          title: '稿件标题',
          key: 'title',
          dataIndex: 'title',
          render: (text: any, record: any) => (
            <Tooltip title={text}>
              <a href={record.article_url} target="_blank">
                {text?.slice(0, 30)}
                {text?.length > 30 && '...'}
              </a>
            </Tooltip>
          ),
        },
        {
          title: (
            <span>
              自定义标题 &emsp;
              <Tooltip
                title={<span>前台榜单页面优先显示自定义标题；未自定义时，显示稿件原本的标题</span>}
              >
                <Icon type="question-circle" />
              </Tooltip>
            </span>
          ),
          key: 'new_title',
          dataIndex: 'new_title',
          render: (text: any, record: any) => (
            <Tooltip title={text}>
              <span>
                {text?.slice(0, 30)}
                {text?.length > 30 && '...'}
              </span>
            </Tooltip>
          ),
        },
        {
          title: '类型',
          key: 'article_type',
          dataIndex: 'article_type',
          width: 80,
          render: (text: any) => ['小视频', '', '短图文', '长文章'][text - 10],
        },
        {
          title: '潮鸣号',
          key: 'cmh_name',
          dataIndex: 'cmh_name',
        },
        {
          title: '叠加热度值',
          key: 'hot_values',
          dataIndex: 'hot_values',
          width: 140,
        },
        {
          title: (
            <span>
              前台显示状态 &emsp;
              <Tooltip
                title={
                  <span>
                    指稿件在本期「优质稿件榜」前台页面是否显示；不影响稿件在客户端内的正常显示
                  </span>
                }
              >
                <Icon type="question-circle" />
              </Tooltip>
            </span>
          ),
          dataIndex: 'hidden',
          render: (text: any) => (text ? '隐藏' : '显示'),
          width: 140,
        },
        {
          title: '操作',
          key: 'op',
          width: 120,
          render: (_: any, record: any, b: number) => (
            <span>
              {record.hidden ? (
                <PermA perm="ranks_cmh:update_data" onClick={() => editHiddenStatus(record)}>
                  设为显示
                </PermA>
              ) : (
                <PermA perm="ranks_cmh:update_data" onClick={() => editHiddenStatus(record)}>
                  设为隐藏
                </PermA>
              )}
              &nbsp;&nbsp;
              <PermA perm="ranks_cmh:update_data" onClick={() => editTitle(record)}>
                编辑
              </PermA>
            </span>
          ),
        },
      ];
    }

    return result;
  };

  const handleGetMonths = () => {
    userApi
      .getCmhRankMonthList({})
      .then(async (res: any) => {
        const list = res?.data?.list || [];
        setRankDate(list);

        const date = list[0] || '';
        let categorys = [];
        if (rank_type == 5) {
          categorys = await handleGetCategorys({ summary_month: date });
        } else if (rank_type == 2) {
          categorys = handleGetLocation();
        }
        setRankCategorys(categorys);
        setFilter({
          ...filter,
          summary_month: date,
          type_value: categorys[0] || '',
        });
      })
      .catch((err) => {});
  };

  const handleGetMonthsWeek = () => {
    userApi
      .getCmhRankWeekList({})
      .then(async (res: any) => {
        const list = res?.data?.list || [];
        setRankDateWeek(list);
        // year  week_number
        const date = list[0] || '';
        let categorys = [];
        if (rank_type == 5) {
          categorys = await handleGetCategorys({
            summary_year: date.summary_year,
            summary_week: date.summary_week_number,
          });
        } else if (rank_type == 2) {
          categorys = handleGetLocation();
        }
        setRankCategorys(categorys);
        setFilter({
          ...filter,
          // summary_month: date,
          summary_year: date.summary_year,
          summary_week: date.summary_week_number,
          type_value: categorys[0] || '',
        });
      })
      .catch((err) => {});
  };

  // 保存健康度
  async function handleModifyRankData(i: number) {
    const record = records[i];
    if (record.new_operation_value === undefined) {
      message.error('请输入对应健康度得分');
      return;
    }
    const params: any = {
      cmh_id: record.cmh_id,
      result_id: id,
      operation_value: record.new_operation_value,
    };
    if (category == 0) {
      params.summary_month = filter.summary_month;
    } else {
      params.summary_year = filter.summary_year;
      params.summary_week = filter.summary_week;
    }
    userApi
      .cmhModifyRankData(params)
      .then((result) => {
        message.success('编辑成功');
        getList();
      })
      .catch((err) => {});
  }

  // 切换编辑状态
  const toggleEdit = (i: number, type: boolean) => {
    records[i].isPointEdit = type;
    forceUpdate();
  };

  const handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      const newFilter = {
        ...filter,
        keyword: search.keyword || '',
      };
      if (rank_type == 4) {
        newFilter.type = search.type;
      }
      setFilter(newFilter);
    }
  };

  const editHiddenStatus = (record: any) => {
    Modal.confirm({
      title: (
        <>
          确定本期榜单<span style={{ color: 'red' }}>{record.hidden ? '显示' : '隐藏'}</span>
          该稿件？
        </>
      ),
      content: '操作后将直接更新前台页面显示（不影响往期榜单）',
      onOk: () => {
        const params: any = {
          rank_id: id,
          hidden: !record.hidden,
          article_id: record.article_id,
        };
        if (category == 0) {
          params.summary_month = filter.summary_month;
        } else {
          params.summary_year = filter.summary_year;
          params.summary_week = filter.summary_week;
        }
        userApi
          .editArticleRankHiddenStatus(params)
          .then((result) => {
            message.success('修改成功');
            getList();
          })
          .catch((err) => {});
      },
    });
  };

  // 手动发榜
  const handlePublish = () => {
    Modal.confirm({
      title: '确定要发榜吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        if (!!publish?.id) {
          userApi
            .cmhManualSinglePublish({ publish_id: publish?.id })
            .then((result) => {
              message.success('手动发榜成功');
              getList();
            })
            .catch((err) => {});
        }
      },
      onCancel: () => {},
    });
  };

  // 更新展示数量
  const updateShowNum = async () => {
    if (!publish?.id) {
      return;
    }
    const num = showCountModal.count;
    if (num == undefined) {
      message.error('请输入展示数量');
      return;
    }
    userApi
      .updateCmhRankShowNum({ publish_id: publish?.id, num })
      .then((result) => {
        message.success('修改成功');
        publish.show_num = num;
        setShowCountModal({ visible: false });
      })
      .catch((err) => {});
  };

  const handleExportData = () => {
    Modal.confirm({
      title: '确定导出当前榜单吗？',
      onOk: () => {
        dispatch(setConfig({ loading: true }));
        const api = rank_type == 4 ? userApi.exportCmgRankArticleData : userApi.exportCmgRankData;
        api({ ...filter })
          .then((res: any) => {
            dispatch(setConfig({ loading: false }));
            const a = document.createElement('a');
            a.href = window.URL.createObjectURL(res.data);
            if (category == 0) {
              a.download = `${rank_name}_月榜_${publish.summary_month}.xlsx`;
            } else {
              a.download = `${rank_name}_周榜_${publish.summary_start_date}-${publish.summary_end_date}.xlsx`;
            }
            a.click();
          })
          .catch((err) => {
            dispatch(setConfig({ loading: false }));
          });
      },
    });
  };

  // 获取行业类别
  const handleGetCategorys = async (params: any) => {
    try {
      const res: any = await userApi.getCmhRankCategorys(params);
      const list = res?.data?.list || [];
      return list;
    } catch (error) {
      return [];
    }
  };

  // 获取城市
  const handleGetLocation = () => {
    const list = [
      '杭州市',
      '宁波市',
      '温州市',
      '湖州市',
      '嘉兴市',
      '绍兴市',
      '金华市',
      '衢州市',
      '舟山市',
      '台州市',
      '丽水市',
    ];
    return list;
  };

  const handleChageRankDate = async (value: any) => {
    let type_value = filter.type_value;
    if (rank_type == 5) {
      const categorys = await handleGetCategorys({ summary_month: value });
      type_value = categorys[0];
      setRankCategorys(categorys || []);
    }

    setFilter({
      ...filter,
      summary_month: value,
      type_value: type_value,
    });
  };

  const handleChageRankDateWeek = async (value: any) => {
    const [week, year] = value.split('-');
    setFilter({ ...filter, summary_week: week, summary_year: year });

    let type_value = filter.type_value;
    if (rank_type == 5) {
      const categorys = await handleGetCategorys({ summary_week: week, summary_year: year });
      type_value = categorys[0];
      setRankCategorys(categorys || []);
    }

    setFilter({
      ...filter,
      summary_week: week,
      summary_year: year,
      type_value: type_value,
    });
  };

  let tab_name = '';
  if (rank_type === 5 || rank_type === 2) {
    tab_name = filter.type_value || rankCategorys[0];
  }
  const host: any =
    {
      dev: 'https://tmtest.tidenews.com.cn',
      test: 'https://tmtest.tidenews.com.cn',
      prev: 'https://tmprev.tidenews.com.cn',
      prod: 'https://tidenews.com.cn',
      testb: 'https://tmtest.tidenews.com.cn',
    }[BUILD_ENV] || 'https://tmtest.tidenews.com.cn';

  // 后期域名需要切换
  let rank_url = `${host}/chao-rank/chaoMing?tenantId=1&current_tab=${tab_name || ''}&rank_id=${
    id || ''
  }&rank_type=${rank_type || ''}&category=${category}`;

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button style={{ marginRight: 8 }} onClick={() => history.goBack()}>
            返回
          </Button>
          <ReactClipboard
            action="copy"
            text={rank_url}
            onSuccess={() => message.success('链接已复制')}
            onError={() => message.error('复制失败')}
          >
            <a style={{ display: 'none' }} id="link">
              xxxx
            </a>
          </ReactClipboard>
          <Button
            style={{ marginRight: 8 }}
            onClick={() => {
              const myLink = document.getElementById('link');
              myLink?.click();
            }}
          >
            复制榜单链接
          </Button>
          <Button
            style={{ marginRight: 8 }}
            onClick={() => setRankInstructionsModal({ visible: true, skey: Date.now() })}
          >
            榜单说明
          </Button>
          <PermButton
            perm=""
            onClick={() => setShowCountModal({ visible: true, count: publish?.show_num || 1 })}
          >
            展示数量
          </PermButton>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb([...props.breadCrumb, rank_name])}
        </Col>
      </Row>
      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={rank_type == 4 ? 10 : 16}>
            {rankCategorys.length > 0 && (
              <>
                <span>{rank_type == 5 ? '行业类型' : '地市'}:&nbsp;</span>
                <Select
                  value={filter.type_value}
                  onChange={(v) => setFilter({ ...filter, type_value: v })}
                  style={{ width: 140, marginRight: 8 }}
                >
                  {rankCategorys.map((v: any) => (
                    <Select.Option value={v} key={v}>
                      {v}
                    </Select.Option>
                  ))}
                </Select>
              </>
            )}
            {category == 0 ? (
              <Select
                value={filter.summary_month}
                style={{ width: 140, marginRight: 8 }}
                onChange={(value: string) => handleChageRankDate(value)}
              >
                {rank_dates.map((v: any) => (
                  <Select.Option value={v} key={v}>
                    {moment(`${v}`, 'YYYYMM').format('YYYY.MM')}
                  </Select.Option>
                ))}
              </Select>
            ) : (
              <Select
                value={
                  !!filter.summary_week
                    ? `${filter.summary_week}-${filter.summary_year}`
                    : undefined
                }
                style={{ width: 300, marginRight: 8 }}
                onChange={(value: string) => handleChageRankDateWeek(value)}
              >
                {rank_dates_week.map((v: any) => {
                  return (
                    <Select.Option
                      value={`${v.summary_week_number}-${v.summary_year}`}
                      key={`${v.summary_week_number}-${v.summary_year}`}
                    >
                      {v.summary_start_date}-{v.summary_end_date}
                    </Select.Option>
                  );
                })}
              </Select>
            )}

            <PermButton perm="" style={{ marginRight: 8 }} onClick={() => handleExportData()}>
              导出当前榜单
            </PermButton>
            <PermButton perm="ranks_cmh:publish" type="primary" onClick={() => handlePublish()}>
              手动发榜
            </PermButton>
            {rank_status ? (
              <span style={{ marginLeft: 8 }}>已发榜</span>
            ) : (
              <span style={{ marginLeft: 8 }}>未发榜</span>
            )}
          </Col>
          <Col span={rank_type == 4 ? 14 : 8} style={{ textAlign: 'right' }}>
            {rank_type == 4 && (
              <>
                <span>筛选：</span>
                <Select
                  value={filter.hidden}
                  style={{ width: 130, marginRight: 8, marginLeft: 8 }}
                  onChange={(e) => {
                    setFilter({ ...filter, hidden: e });
                  }}
                >
                  <Select.Option value="">前台显示状态</Select.Option>
                  <Select.Option value={0}>显示</Select.Option>
                  <Select.Option value={1}>隐藏</Select.Option>
                </Select>
                <Select
                  value={search.type}
                  style={{ width: 150, marginRight: 8, marginLeft: 8 }}
                  onChange={(e) => {
                    setSearch({ ...search, type: e });
                  }}
                >
                  <Select.Option value={1}>潮鸣号名称</Select.Option>
                  <Select.Option value={2}>稿件标题</Select.Option>
                </Select>
              </>
            )}

            <Input
              style={{ width: 160, marginRight: 8 }}
              onKeyPress={(e) => handleKey(e)}
              value={search.keyword}
              placeholder={rank_type == 4 ? '请输入搜索内容' : '搜索潮鸣号名称'}
              onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
            />
            <Button onClick={() => handleKey({ which: 13 })}>
              <Icon type="search" />
              搜索
            </Button>
          </Col>
        </Row>
        <Row style={{ marginBottom: 16 }}>
          阅读数：{publish?.pv || 0}&nbsp;&nbsp;转发量：{publish?.share_count || 0}
        </Row>
        <Table
          columns={getColumns()}
          rowKey={rank_type == 4 ? 'article_id' : 'id'}
          func={rank_type == 4 ? 'getSingleCmhArticleList' : 'getSingleCmhRankList'}
          index="page"
          filter={filter}
          pagination={true}
        />

        {/* 榜单说明 */}
        <RankInstructionsModal
          type={1}
          id={id}
          {...rankInstructionsModal}
          desc_switch={rank?.desc_switch}
          description={rank?.description}
          onCancel={() => setRankInstructionsModal({ visible: false })}
          onEnd={() => {
            setRankInstructionsModal({ visible: false });
            getList();
          }}
        ></RankInstructionsModal>

        {/* 展示数量 */}
        <Modal
          visible={showCountModal.visible}
          onCancel={() => setShowCountModal({ visible: false })}
          onOk={() => updateShowNum()}
          title="展示数量"
          width={500}
        >
          <InputNumber
            min={0}
            max={100}
            value={showCountModal.count}
            onChange={(v: any) => setShowCountModal({ ...showCountModal, count: v })}
          />
        </Modal>
      </div>
    </>
  );
}
