import { getTableList } from '@action/tableList';
import { userApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, Table, Drawer } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm, setLoading, setMenu } from '@utils/utils';
import {
  Button,
  Col,
  Dropdown,
  Form,
  Icon,
  Input,
  Menu,
  message,
  Modal,
  Radio,
  Row,
  Select,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';
import WaistcoatUserForm from '@components/business/waistcoatUserForm';
import ChangeMobileModal from './changeMobileModal';

@(withRouter as any)
@connect
class WaistcoatUser extends React.Component<any, any> {
  formRef: any;

  constructor(props: any) {
    super(props);
    this.state = {
      filter: {
        status: '-1',
        keyword: '',
        searchType: '0',
        official_cert_status: '',
      },
      currentType: '1',
      currentKeyword: '',
      key: Date.now(),
      detail: {},
      drawer: {
        visible: false,
        key: Date.now(),
      },
      formContent: {},
      officialCertForm: {
        visible: false,
        key: Date.now() + 1,
        id: '',
        type: 0,
        info: '',
      },
      form: {
        visible: false,
        key: Date.now(),
        id: '',
        password: '',
        type: 1,
        err: '',
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    setMenu(this);
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(getTableList('getWaistcoatUserList', 'list', { ...filter, ...overlap }));
  };

  getFilter = () => {
    const { status, keyword, searchType, official_cert_status } = this.state.filter;
    const { current, size } = this.props.tableList;
    const filter: any = { current, size };
    if (status && status != -1) {
      filter.status = status;
    }
    if (keyword) {
      filter['search_type'] = searchType;
      filter['keyword'] = keyword;
    }
    if (official_cert_status) {
      filter.official_cert_status = official_cert_status;
    }
    return filter;
  };

  handleKey = (e: any) => {
    if (e.which === 13) {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            searchType: this.state.currentType,
            keyword: this.state.currentKeyword,
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  shadowConvert = (record: any) => {
    Modal.confirm({
      title: record.virtual_type != 2 ? '确定设为马甲号？' : '确定取消马甲号？',
      content:
        record.virtual_type != 2
          ? '设为马甲号后，可用于在评论系统后台发布评论（操作后请联系评论系统负责人，同步最新马甲号数据）'
          : '马甲号改为普通账号后，将无法在评论系统后台使用',
      onOk: () => {
        setLoading(this, true);
        api
          .shadowConvert({ id: record.id, shadow: record.virtual_type != 2 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  toggleWhiteList = (record: any) => {
    Modal.confirm({
      title: record.in_pub_white
        ? '确定要取消该用户的白名单资格吗？'
        : '设置白名单后，该用户在客户端发布内容时，不要求进行实名认证',
      onOk: () => {
        setLoading(this, true);
        api
          .toggleWhiteList({ id: record.id, pub_white: record.in_pub_white ? 0 : 1 })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  resetNickname = (record: any) => {
    Modal.confirm({
      title: '确定将用户昵称重置为随机的默认昵称？',
      onOk: () => {
        setLoading(this, true);
        api
          .resetNickName({ account_id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  resetPassword = (record: any) => {
    this.setState({
      form: {
        visible: true,
        key: Date.now(),
        id: record.id,
        password: '',
        type: 1,
        err: '',
      },
    });
  };

  blackListRecord = (record: any) => {
    Modal.confirm({
      title: record.status === 2 ? '确定从黑名单移除？' : '确定拉入黑名单？',
      content: record.status === 2 ? '' : '拉黑后，用户无法登录APP',
      onOk: () => {
        setLoading(this, true);
        api
          .setUserBlackList({ id: record.id, api: record.status === 2 ? 'unforbid' : 'forbid' })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  resetAvatar = (record: any) => {
    setLoading(this, true);
    api
      .resetUserAvatar({ account_id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  edit = (record: any) => {
    // const ids = r.data.push_notify_list.article_news.map((v: any, i: number) => {
    //   r.data.push_notify_list.article_news[i].list_title = v.title;
    //   r.data.push_notify_list.article_news[i].id = v.article_id;
    //   r.data.push_notify_list.article_news[i].channel_name = v.channel_name;
    //   return v.article_id;
    // });
    let province = null;
    let city = null;
    let cityList = record.location?.split(',');
    if (cityList?.length == 3) {
      province = cityList[1];
      city = cityList[2];
    } else if (cityList?.length == 3) {
      province = cityList[1];
    }

    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
      },
      formContent: {
        ...record,
        province: province,
        city: city,
        // channel_article_ids: ids,
        // channelArticles: r.data.push_notify_list.article_news,
        // area_names: r.data.push_notify_list.area_name
        //   ? r.data.push_notify_list.area_name.split(',')
        //   : ['全局'],
      },
    });
    // this.props.dispatch(setConfig({ loading: false }));
  };

  setOfficialCert = (record: any) => {
    this.setState({
      officialCertForm: {
        visible: true,
        key: Date.now(),
        type: record.official_cert_status || 0,
        info: record.official_cert_info || '',
        id: record.id,
      },
    });
  };

  submitOfficialCert = () => {
    const { id, type, info } = this.state.officialCertForm;
    if (type !== 0 && info.trim() === '') {
      message.error('请填写认证信息');
      return;
    }
    if (type !== 0 && info.length > 25) {
      message.error('认证信息不能超过25个字');
      return;
    }
    setLoading(this, true);
    api
      .updateOfficialCert({
        id,
        info: info?.trim(),
      })
      .then(() => {
        this.closeOfficialCert();
        this.getData();
        setLoading(this, false);
      })
      .catch(() => setLoading(this, false));
  };

  closeOfficialCert = () => {
    this.setState({
      officialCertForm: { ...this.state.officialCertForm, visible: false },
    });
  };

  officialCertTypeChange = (e: any) => {
    this.setState({
      officialCertForm: { ...this.state.officialCertForm, type: e.target.value, info: '' },
    });
  };

  officialCertInfoChange = (e: any) => {
    this.setState({
      officialCertForm: { ...this.state.officialCertForm, info: e.target.value },
    });
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;
    const getDropDown = (record: any) => {
      const menu = (
        <Menu>
          {requirePerm(
            this,
            'account:shadow_edit'
          )(<Menu.Item onClick={() => this.edit(record)}>编辑</Menu.Item>)}
          {requirePerm(
            this,
            'account:reset_portrait'
          )(<Menu.Item onClick={() => this.resetAvatar(record)}>重置头像</Menu.Item>)}
          {/* {record.status === 1 &&
            requirePerm(
              this,
              'account:log_off'
            )(<Menu.Item onClick={() => this.cancelDelete(record)}>解除注销</Menu.Item>)} */}
          {/* {requirePerm(
            this,
            'account:update_user_cert'
          )(<Menu.Item onClick={() => this.setCert(record)}>设置认证</Menu.Item>)} */}
          {/* {requirePerm(
            this,
            'account:update_user_tag'
          )(
            <Menu.Item onClick={() => this.setUserTag(record)}>
              {record.user_tag === 1 ? '取消' : '设为'}顺丰侠
            </Menu.Item>
          )} */}
          {requirePerm(
            this,
            'account:update_official_cert'
          )(<Menu.Item onClick={() => this.setOfficialCert(record)}>官方认证</Menu.Item>)}
          {requirePerm(
            this,
            'account_forbid:forbid'
          )(
            <Menu.Item onClick={() => this.blackListRecord(record)}>
              {record.status === 2 ? '取消' : ''}黑名单
            </Menu.Item>
          )}
          {/* {requirePerm(this, 'account:reset_nick_name')(
            <Menu.Item onClick={() => this.resetNickname(record)}>重置昵称</Menu.Item>
          )} */}

          {requirePerm(
            this,
            'account:update_account_pwd:normal'
          )(<Menu.Item onClick={this.resetPassword.bind(this, record)}>重置密码</Menu.Item>)}

          {requirePerm(
            this,
            'account:shadow_convert'
          )(<Menu.Item onClick={this.shadowConvert.bind(this, record)}>取消马甲号</Menu.Item>)}

          {requirePerm(
            this,
            'account:emergency_phone'
          )(<Menu.Item onClick={this.resetMobile.bind(this, record)}>紧急联系人</Menu.Item>)}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };
    return [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '用户昵称',
        key: 'nick_name',
        dataIndex: 'nick_name',
      },
      {
        title: '小潮号',
        key: 'chao_id',
        dataIndex: 'chao_id',
        width: 120,
      },
      {
        title: '用户头像',
        key: 'image_url',
        dataIndex: 'image_url',
        render: (text: string) => <img src={text} className="list-pic" alt="" />,
        width: 90,
      },
      // {
      //   title: '认证信息',
      //   key: 'cert_type',
      //   dataIndex: 'cert_type',
      //   render: (text: number) => <span>{['', '个人认证', '机构认证'][text]}</span>,
      //   width: 90,
      // },
      {
        title: '绑定手机号',
        key: 'phone_number',
        dataIndex: 'phone_number',
        width: 150,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: any, record: any) => <span>{record.status == 0 ? '正常' : '黑名单'}</span>,
        width: 90,
      },
      {
        title: '官方认证',
        key: 'official_cert_status',
        dataIndex: 'official_cert_status',
        render: (text: number) => <span>{['否', '是'][text]}</span>,
        width: 90,
      },
      // {
      //   title: '实人认证',
      //   key: 'real',
      //   dataIndex: 'face_cert',
      //   render: (text: number) => <span>{['未认证', '未认证', '已认证', '已认证'][text]}</span>,
      //   width: 90,
      // },
      {
        title: '注册时间',
        key: 'created_at',
        dataIndex: 'created_at',
        render: (text: any) => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
        width: 170,
      },
      {
        title: '用户ID',
        key: 'id',
        dataIndex: 'id',
        width: 220,
      },
      {
        title: <span>操作</span>,
        key: 'op',
        render: (text: any, record: any) => getDropDown(record),
        width: 70,
      },
    ];
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确认删除特权组“{record.group_name}”吗？</p>,
      onOk: () => {
        setLoading(this, true);
        api
          .deletePrivilegeGroup({ id: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            setLoading(this, false);
          })
          .catch(() => setLoading(this, false));
      },
    });
  };

  filterChange = (type: string, value: any) => {
    this.setState(
      {
        filter: { ...this.state.filter, [type]: value },
      },
      () => this.getData({ current: 1 })
    );
  };

  createUser = () => {
    this.setState({
      drawer: {
        visible: true,
        key: Date.now(),
      },
      formContent: {},
    });
  };

  closeDrawer = () => {
    this.setState({
      drawer: { ...this.state.drawer, visible: false },
    });
  };

  onSubmitEnd = () => {
    this.setState({
      drawer: { ...this.state.drawer, visible: false },
    });
    // if (searchToObject().channel_id && searchToObject().tmId) {
    //   this.props.history.push('/view/waistcoatUser');
    // }
    this.getData();
  };

  setFormType = (value: 1 | 2) => {
    this.setState({
      form: {
        ...this.state.form,
        type: value,
      },
    });
  };

  passwordChange = (e: any) => {
    const { value } = e.target;
    const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]+$/;
    let err = '';
    if (value.length < 8 || !regex.test(value)) {
      err = '密码为8-30位，仅支持数字和字母的组合';
    }
    this.setState({
      form: {
        ...this.state.form,
        err,
        password: value,
      },
    });
  };

  closeModal = () => {
    this.setState({
      form: {
        ...this.state.form,
        visible: false,
      },
    });
  };

  resetMobile = (record: any) => {
    this.setState({
      changeMobileModal: {
        visible: true,
        record,
        key: Date.now(),
      },
    });
  };

  submit = () => {
    const { form } = this.state;
    if ((form.type === 1 && form.err) || (form.type === 1 && form.password.length < 8)) {
      message.error('密码为8-30位，仅支持数字和字母的组合');
      return;
    }
    const body: CommonObject = {
      account_id: form.id,
    };
    if (form.type === 1) {
      body.pwd = form.password;
    } else {
      body.pwd = '';
    }
    setLoading(this, true);
    api
      .resetOrgUserPassword(body)
      .then((r: any) => {
        setLoading(this, false);
        this.closeModal();
        Modal.info({
          title: '重置成功',
          content: r.data.pwd,
        });
      })
      .catch(() => {
        setLoading(this, false);
      });
  };

  render() {
    const { filter, drawer, officialCertForm, form } = this.state;
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    return (
      <React.Fragment>
        <Row className="layout-infobar">
          <Col span={12}>
            <Button onClick={() => this.props.history.push('/view/userList')}>
              <Icon type="left-circle" /> 返回用户列表
            </Button>
            {requirePerm(
              this,
              ''
            )(
              <Button onClick={() => this.createUser()} style={{ marginLeft: 8 }}>
                <Icon type="plus-circle" /> 创建用户
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Select
                value={filter.status}
                onChange={this.filterChange.bind(this, 'status')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="-1">用户状态</Select.Option>
                <Select.Option value="0">正常</Select.Option>
                <Select.Option value="2">黑名单</Select.Option>
              </Select>

              <Select
                value={filter.official_cert_status}
                onChange={this.filterChange.bind(this, 'official_cert_status')}
                style={{ marginRight: 8, width: 120 }}
              >
                <Select.Option value="">官方认证</Select.Option>
                <Select.Option value="1">是</Select.Option>
                <Select.Option value="0">否</Select.Option>
              </Select>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Select
                value={this.state.currentType}
                style={{ width: 130, marginRight: 8, marginLeft: 8 }}
                onChange={(v: any) => this.setState({ currentType: v })}
              >
                <Select.Option value="1">搜索用户昵称</Select.Option>
                <Select.Option value="3">搜索用户ID</Select.Option>
                <Select.Option value="2">搜索手机号</Select.Option>
                <Select.Option value="4">搜索小潮号</Select.Option>
              </Select>
              <Input
                value={this.state.currentKeyword}
                style={{ marginRight: 8, width: 120 }}
                onChange={(e: any) => this.setState({ currentKeyword: e.target.value })}
                onKeyPress={this.handleKey}
                placeholder="输入搜索内容"
              />
              <Button onClick={() => this.handleKey({ which: 13 })}>
                <Icon type="search" /> 搜索
              </Button>
            </Col>
          </Row>
          <Table
            func="getWaistcoatUserList"
            index="list"
            rowKey="id"
            filter={this.getFilter()}
            columns={this.getColumns()}
            pagination={true}
          />
          <Drawer
            visible={drawer.visible}
            skey={drawer.key}
            title="创建/编辑马甲用户"
            onClose={this.closeDrawer}
            onOk={() => this.formRef.doSubmit()}
          >
            <WaistcoatUserForm
              onEnd={this.onSubmitEnd}
              formContent={this.state.formContent}
              // eslint-disable-next-line no-return-assign
              wrappedComponentRef={(instance: any) => (this.formRef = instance)}
            />
          </Drawer>

          {/* 官方认证 */}
          <Modal
            visible={officialCertForm.visible}
            key={officialCertForm.key}
            title="设置官方认证"
            onCancel={this.closeOfficialCert}
            onOk={this.submitOfficialCert}
          >
            <Form {...formLayout}>
              <Form.Item required={true} label="官方认证">
                <Radio.Group value={officialCertForm.type} onChange={this.officialCertTypeChange}>
                  <Radio value={0}>否</Radio>
                  <Radio value={1}>是</Radio>
                </Radio.Group>
                {/* <p style={{
                                    height: 14,
                                    color: '#aaa',
                                    marginTop: -8
                                }}>新的潮鸣号体系下，个人或机构认证均是潮鸣号</p> */}
              </Form.Item>
              {officialCertForm.type !== 0 && (
                <Form.Item required={true} label="认证信息">
                  <Input
                    placeholder="最多25字"
                    value={officialCertForm.info}
                    onChange={this.officialCertInfoChange}
                  />
                </Form.Item>
              )}
            </Form>
          </Modal>

          <ChangeMobileModal
            {...this.state.changeMobileModal}
            onCancel={() =>
              this.setState({
                changeMobileModal: { ...this.state.changeMobileModal, visible: false },
              })
            }
            onEnd={() => {
              this.setState({
                changeMobileModal: { ...this.state.changeMobileModal, visible: false },
              });
              this.getData();
            }}
          ></ChangeMobileModal>

          <Modal
            visible={form.visible}
            key={form.key}
            title="重置密码"
            onCancel={this.closeModal}
            onOk={this.submit}
          >
            <Row style={{ marginBottom: 16 }}>
              <Radio checked={form.type === 1} onClick={this.setFormType.bind(this, 1)}>
                人工设置&nbsp;&nbsp;
                <Input
                  value={form.password}
                  onChange={this.passwordChange}
                  placeholder="请输入新密码"
                  maxLength={30}
                  onFocus={this.setFormType.bind(this, 1)}
                  style={{ width: 140 }}
                />
                <span style={{ color: 'red' }}>&nbsp;{form.err}</span>
              </Radio>
            </Row>
            <Row>
              <Radio checked={form.type === 2} onClick={this.setFormType.bind(this, 2)}>
                随机生成
              </Radio>
            </Row>
          </Modal>
        </div>
      </React.Fragment>
    );
  }
}

export default WaistcoatUser;
