import { setConfig } from '@action/config';
import { getTableList } from '@action/tableList';
import { opApi as api } from '@app/api';
import { CommonObject } from '@app/types';
import { A, ImageUploader, Table, VideoUploader } from '@components/common';
import connect from '@utils/connectTable';
import { getCrumb, requirePerm } from '@utils/utils';
import {
  Button,
  Col,
  DatePicker,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Switch,
  Tooltip,
} from 'antd';
import moment from 'moment';
import React from 'react';
import { withRouter } from 'react-router';

import ImagePreviewColumn from '@app/components/common/imagePreviewColumn';

@(withRouter as any)
@connect
@(Form.create({ name: 'startPageForm' }) as any)
class StartPageManager extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      editRecord: {
        id: '',
        visible: false,
        mtitle: '',
        key: Date.now(),
        url: '',
        delay_time: null,
        pic_url: '',
        title: '',
        video_url: '',
        view_type: 0,
      },
      searchState: {
        search_type: 0,
        keyword: '',
      },
      filter: {
        view_type: '',
        begin: '',
        end: '',
        search_type: 0,
        keyword: '',
      },
      video: {
        visible: false,
        url: true,
      },
    };
  }

  componentDidMount() {
    this.getData({ current: 1, size: 10 });
    this.props.dispatch(
      setConfig({
        openKeys: this.props.openKeys,
        selectKeys: this.props.selectKeys,
      })
    );
  }

  getData = (overlap: CommonObject = {}, filter = this.getFilter()) => {
    this.props.dispatch(
      getTableList('getStartPageList', 'app_start_page_list', { ...filter, ...overlap })
    );
  };

  getFilter = () => {
    const { current, size } = this.props.tableList;
    const filter: CommonObject = { ...this.state.filter, current, size, type: 1 };
    if (!filter.keyword) {
      delete filter.search_type;
    }
    return filter;
  };

  editRecord = (record: any) => {
    this.setState({
      editRecord: {
        id: record.id,
        visible: true,
        mtitle: '编辑启动页',
        key: Date.now(),
        url: record.url,
        pic_url: record.view_type === 1 ? '' : record.pic_url,
        title: record.title,
        delay_time: record.delay_time ? moment(record.delay_time) : null,
        off_time: record.off_time ? moment(record.off_time) : null,
        video_url: record.view_type === 0 ? '' : record.pic_url,
        view_type: record.view_type,
        allowUpdate: !(record.status === 4 && this.props.tableList.allData.on_show_pages >= 100),
        requireTime: true,
        bottom_logo: record.bottom_logo || 0,
      },
    });
    this.props.form.setFieldsValue({
      url: record.url,
      pic_url: record.view_type === 1 ? '' : record.pic_url,
      title: record.title,
      delay_time: record.delay_time ? moment(record.delay_time) : null,
      video_url: record.view_type === 0 ? '' : record.pic_url,
      view_type: record.view_type,
      off_time: record.off_time ? moment(record.off_time) : null,
      bottom_logo: record.bottom_logo || 0,
    });
  };

  updateStatus = (record: any) => {
    this.props.dispatch(setConfig({ loading: true }));
    api
      .offStartPage({ id: record.id })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  toDetail = (record: any, type: string) => {
    this.props.history.push(`/view/startPageDevices/${type}/${record.id}`);
  };

  exchangeOrder = (index: number, offset: number) => {
    const { records } = this.props.tableList;
    console.log(records[index], records[index + offset]);
    this.props.dispatch(setConfig({ loading: true }));
    api
      .updateStartPageOrder({
        from_id: records[index].id,
        to_id: records[index + offset].id,
      })
      .then(() => {
        message.success('操作成功');
        this.getData();
        this.props.dispatch(setConfig({ loading: false }));
      })
      .catch(() => this.props.dispatch(setConfig({ loading: false })));
  };

  getColumns = () => {
    const { current, size } = this.props.tableList;
    const getSeq = (i: number) => (current - 1) * size + i + 1;

    const getDropdown = (record: any) => {
      const menu = (
        <Menu>
          {record.view_type === 0 && (
            <Menu.Item onClick={() => this.toDetail(record, 'view')}>查看设备图</Menu.Item>
          )}
          {requirePerm(
            this,
            'app_start_page:update'
          )(<Menu.Item onClick={() => this.editRecord(record)}>编辑启动页</Menu.Item>)}
          {record.view_type === 0 &&
            requirePerm(
              this,
              'app_start_page:update'
            )(<Menu.Item onClick={() => this.toDetail(record, 'edit')}>编辑设备图</Menu.Item>)}
          {requirePerm(
            this,
            'app_start_page:off'
          )(
            <Menu.Item
              onClick={() => this.updateStatus(record)}
              disabled={record.status === 1 || record.status === 4}
            >
              下架
            </Menu.Item>
          )}
          {requirePerm(
            this,
            'app_start_page:delete'
          )(<Menu.Item onClick={() => this.deleteRecord(record)}>删除</Menu.Item>)}
        </Menu>
      );
      return (
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link">
            操作 <Icon type="down" />
          </a>
        </Dropdown>
      );
    };

    const canOrderCount = this.props.tableList.records.filter((v: any) => v.status === 3).length;

    const orderColumn: any = [
      {
        title: '排序',
        key: 'order',
        render: (text: any, record: any, i: number) =>
          record.status === 3 ? (
            <span>
              {requirePerm(
                this,
                ''
              )(
                <A
                  disabled={getSeq(i) === 1 || getSeq(i) > canOrderCount}
                  className="sort-up"
                  onClick={() => this.exchangeOrder(i, -1)}
                >
                  <Icon type="up-circle" theme="filled" />
                </A>
              )}{' '}
              {requirePerm(
                this,
                ''
              )(
                <A
                  disabled={getSeq(i) >= canOrderCount}
                  className="sort-down"
                  onClick={() => this.exchangeOrder(i, 1)}
                >
                  <Icon type="down-circle" theme="filled" />
                </A>
              )}
            </span>
          ) : (
            ''
          ),
        width: 70,
      },
    ];

    const column: any = [
      {
        title: '序号',
        key: 'seq',
        render: (text: any, record: any, i: number) => <span>{getSeq(i)}</span>,
        width: 70,
      },
      {
        title: '启动页主题',
        dataIndex: 'title',
        key: 'title',
        render: (text: any, record: any) => {
          return (
            <Tooltip title={text}>
              {record.view_type === 0 ? (
                <A className="line-max-2" onClick={() => this.toDetail(record, 'view')}>
                  {text}
                </A>
              ) : (
                <span className="line-max-2">{text}</span>
              )}
            </Tooltip>
          );
        },
        width: 100,
      },
      {
        title: '链接地址',
        key: 'url',
        dataIndex: 'url',
        render: (text: any) => (
          <Tooltip title={text}>
            <a className="line-max-2" href={text} target="_blank" rel="noreferrer">
              {text}
            </a>
          </Tooltip>
        ),
      },
      {
        title: '素材',
        key: 'pic_url',
        dataIndex: 'pic_url',
        align: 'center',
        render: (text: any, record: any) => {
          return (
            <div style={{ height: 60, textAlign: 'center' }}>
              {record.view_type == 0 ? (
                <ImagePreviewColumn text={text} imgs={[text]}></ImagePreviewColumn>
              ) : (
                <video
                  style={{ cursor: 'pointer' }}
                  onClick={() => this.showVideoPreview(text)}
                  className="list-pic"
                  src={text}
                ></video>
              )}
            </div>
          );
        },
      },
      {
        title: '类型',
        key: 'view_type',
        dataIndex: 'view_type',
        render: (text: number) => <span>{['图片', '视频'][text]}</span>,
        width: 70,
      },
      {
        title: '计划上架时间',
        dataIndex: 'delay_time',
        render: (text: any) =>
          text ? (
            <>
              <span>{moment(text).format('YYYY-MM-DD')}</span>
              <br />
              <span>{moment(text).format('HH:mm:ss')}</span>
            </>
          ) : (
            <span>未设置</span>
          ),
        width: 100,
      },
      {
        title: '计划下架时间',
        dataIndex: 'off_time',
        render: (text: any) =>
          text ? (
            <>
              <span>{moment(text).format('YYYY-MM-DD')}</span>
              <br />
              <span>{moment(text).format('HH:mm:ss')}</span>
            </>
          ) : (
            <span>未设置</span>
          ),
        width: 100,
      },
      {
        title: '创建人',
        key: 'created_by',
        dataIndex: 'creator',
        width: 110,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (text: number) => <span>{['', '待展示', '计划中', '展示中', '已下架'][text]}</span>,
        width: 90,
      },
      {
        title: '操作',
        key: 'op',
        render: (text: any, record: any) => getDropdown(record),
        width: 70,
      },
    ];

    if (this.state.filter.view_type || !!this.state.filter.begin || !!this.state.filter.keyword) {
      return column;
    }
    return orderColumn.concat(column);
  };

  deleteRecord = (record: any) => {
    Modal.confirm({
      title: <p>确定删除启动页“{record.title}”？</p>,
      onOk: () => {
        this.props.dispatch(setConfig({ loading: true }));
        api
          .deleteStartPage({ ids: record.id })
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.props.dispatch(setConfig({ loading: false }));
          })
          .catch(() => this.props.dispatch(setConfig({ loading: false })));
      },
    });
  };

  handleAdd = () => {
    this.setState({
      editRecord: {
        id: '',
        visible: true,
        mtitle: '添加启动页',
        key: Date.now(),
        url: '',
        pic_url: '',
        title: '',
        delay_time: null,
        video_url: '',
        view_type: 0,
        requireTime: this.props.tableList.allData.on_show_pages < 100,
        off_time: null,
        bottom_logo: 0,
      },
    });
    this.props.form.setFieldsValue({
      url: '',
      pic_url: '',
      title: '',
      delay_time: null,
      off_time: null,
      video_url: '',
      view_type: 0,
      bottom_logo: 0,
    });
  };

  handleSubmitAdd = () => {
    const fields = ['bottom_logo', 'url', 'title', 'delay_time', 'view_type', 'off_time'];
    const vtvalues = this.props.form.getFieldsValue(['view_type']);
    if (vtvalues.view_type === 1) {
      fields.push('video_url');
    } else {
      fields.push('pic_url');
    }
    this.props.form.validateFieldsAndScroll(fields, (err: any, values: any) => {
      if (!err) {
        let func = 'createStartPage';
        const body: any = {
          ...values,
        };
        body.off_time = values.off_time ? values.off_time.format('YYYY-MM-DD HH:mm:ss') : '';
        if (this.state.editRecord.requireTime) {
          body.delay_time = values.delay_time.format('YYYY-MM-DD HH:mm:ss');
          if (values.off_time && values.off_time.isBefore(values.delay_time)) {
            message.error('计划下架时间不能早于计划上架时间');
            return;
          }
        } else {
          delete body.delay_time;
        }
        if (body.view_type === 1) {
          body.pic_url = values.video_url;
          delete body.video_url;
        }
        if (this.state.editRecord.id) {
          func = 'updateStartPage';
          body.id = this.state.editRecord.id;
          if (values.delay_time.isAfter(moment()) && !this.state.editRecord.allowUpdate) {
            message.error('展示中和待展示的启动页不能大于100条，请先下架再编辑');
            this.setState({ loading: false });
            return;
          }
        }
        this.setState({ loading: true });
        api[func as keyof typeof api](body)
          .then(() => {
            message.success('操作成功');
            this.getData();
            this.setState({
              editRecord: { ...this.state.editRecord, visible: false },
              loading: false,
            });
          })
          .catch(() => this.setState({ loading: false }));
      } else {
        message.error('请检查表单项');
      }
    });
  };

  validateUrl = (rule: any, value: any, callback: any) => {
    const regex = /^https?:\/\//;
    if (value.length > 0 && !regex.test(value)) {
      callback('请正确填写关联链接');
      return;
    }
    callback();
  };

  filterChange = (key: string, value: any) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          [key]: value,
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  showCarouselModal = () => {
    let time = this.props.tableList.allData.carousel_times || 0;
    let ruleType = this.props.tableList.allData.rule_type || 1;
    let modal: any;
    const radioStyle = {
      display: 'block',
      height: '30px',
      lineHeight: '30px',
    };
    const spanStyle = {
      verticalAlign: 'top',
      lineHeight: '30px',
      display: 'inline-block',
    };
    const timeChange = (e: any) => {
      time = e || 1;
    };
    const ruleChange = (e: any) => {
      ruleType = e.target.value;
      modal.update({
        content: (
          <>
            <p>
              <span style={spanStyle}>展示规则：</span>
              <span style={spanStyle}>
                <Radio.Group value={ruleType} onChange={ruleChange}>
                  <Radio style={radioStyle} value={1}>
                    每次打开APP按照指定时间间隔轮播一张
                  </Radio>
                  <Radio style={radioStyle} value={2}>
                    每次打开APP依次展示（最多展示前5张）
                  </Radio>
                  {/* <Radio style={radioStyle} value={3}>
                    每次打开APP仅展示一个，下次打开展示下一个&nbsp;
                    <Tooltip title="若启动页被排序、上下架、删除、修改，则重新展示">
                      <Icon type="question-circle" />
                    </Tooltip>
                  </Radio> */}
                </Radio.Group>
              </span>
            </p>
            {ruleType === 1 && (
              <p>
                轮播间隔：
                <InputNumber
                  onChange={timeChange}
                  defaultValue={time}
                  min={1}
                  max={60}
                  step={1}
                  precision={0}
                />
                分钟
              </p>
            )}
          </>
        ),
      });
    };

    modal = Modal.confirm({
      width: 600,
      title: '设置轮播间隔',
      content: (
        <>
          <p>
            <span style={spanStyle}>展示规则：</span>
            <span style={spanStyle}>
              <Radio.Group value={ruleType} onChange={ruleChange}>
                <Radio style={radioStyle} value={1}>
                  每次打开APP按照指定时间间隔轮播一张
                </Radio>
                <Radio style={radioStyle} value={2}>
                  每次打开APP依次展示（最多展示前5张）
                </Radio>
                {/* <Radio style={radioStyle} value={3}>
                  每次打开APP仅展示一个，下次打开展示下一个&nbsp;
                  <Tooltip title="若启动页被排序、上下架、删除、修改，则重新展示">
                    <Icon type="question-circle" />
                  </Tooltip>
                </Radio> */}
              </Radio.Group>
            </span>
          </p>
          {ruleType === 1 && (
            <p>
              轮播间隔：
              <InputNumber
                onChange={timeChange}
                defaultValue={time}
                min={1}
                max={60}
                step={1}
                precision={0}
              />
              分钟
            </p>
          )}
        </>
      ),
      onOk: (closeFunc: any) => {
        if (ruleType === 1 && (!time || time < 1 || time > 60)) {
          message.error('请正确填写时间(1-60)');
          return;
        }
        api.updateDisplayRule({ carousel_time: time, rule_type: ruleType }).then(() => {
          message.success('操作成功');
          this.getData();
          closeFunc();
        });
      },
    });
  };

  // 时间筛选
  dateChange = (_: any, dateString: string[], goToFirstPage = true) => {
    this.setState(
      {
        filter: {
          ...this.state.filter,
          begin: dateString[0],
          end: dateString[1],
        },
      },
      () => this.getData({ current: 1 })
    );
  };

  handleKey = (e: { which: number }) => {
    if (e.which === 13) {
      this.setState(
        {
          filter: {
            ...this.state.filter,
            search_type: this.state.searchState.search_type,
            keyword: this.state.searchState.keyword,
          },
        },
        () => this.getData({ current: 1 })
      );
    }
  };

  showVideoPreview = (url: string) => {
    this.setState({
      video: {
        visible: true,
        url,
      },
    });
  };

  render() {
    const form = this.state.editRecord;
    const { getFieldDecorator } = this.props.form;
    const formLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
    };
    const values = this.props.form.getFieldsValue(Object.keys(form));
    const diabledOffTime = (current: any) => {
      if (!values.delay_time) {
        return true;
      }
      return current && current.isBefore(moment(values.delay_time).startOf('day'));
    };
    return (
      <>
        <Row className="layout-infobar">
          <Col span={12}>
            {requirePerm(
              this,
              'app_start_page:create'
            )(
              <Button onClick={this.handleAdd} style={{ marginRight: 8 }}>
                <Icon type="plus-circle" />
                添加启动页
              </Button>
            )}
            {requirePerm(
              this,
              'app_start_page:carousel'
            )(
              <Button onClick={this.showCarouselModal}>
                <Icon type="form" />
                展示规则
              </Button>
            )}
          </Col>
          <Col span={12} className="layout-breadcrumb">
            {getCrumb(this.props.breadCrumb)}
          </Col>
        </Row>
        <div className="component-content">
          <Row style={{ marginBottom: 16 }}>
            <Col span={14}>
              <Select
                value={this.state.filter.view_type}
                style={{ width: 120, marginRight: 8 }}
                onChange={(v: any) => this.filterChange('view_type', v)}
              >
                <Select.Option value="">全部类型</Select.Option>
                <Select.Option value="0">图片</Select.Option>
                <Select.Option value="1">视频</Select.Option>
              </Select>

              <DatePicker.RangePicker
                style={{ width: 240, marginRight: 8 }}
                onChange={this.dateChange.bind(this)}
                format="YYYY-MM-DD"
              />
              <Tooltip title={'筛选上架时间'}>
                <Icon type="question-circle" style={{ marginLeft: 8, marginRight: 8 }} />
              </Tooltip>
            </Col>
            <Col span={10} style={{ textAlign: 'right' }}>
              <Select
                value={this.state.searchState.search_type}
                style={{ width: 110, marginRight: 8 }}
                onChange={(search_type: any) =>
                  this.setState({
                    searchState: { ...this.state.searchState, search_type },
                  })
                }
              >
                <Select.Option value={0}>启动页主题</Select.Option>
                <Select.Option value={1}>创建人</Select.Option>
              </Select>
              <Input
                value={this.state.searchState.keyword}
                style={{ marginRight: 8, width: 160 }}
                onChange={(e: any) => {
                  this.setState({
                    searchState: { ...this.state.searchState, keyword: e.target.value },
                  });
                }}
                onKeyPress={this.handleKey.bind(this)}
                placeholder="请输入关键词"
              />
              <Button onClick={() => this.handleKey({ which: 13 })}>
                <Icon type="search" /> 搜索
              </Button>
            </Col>
          </Row>

          <Table
            filter={this.getFilter()}
            index="app_start_page_list"
            func="getStartPageList"
            columns={this.getColumns()}
            rowKey="id"
            pagination={true}
          />
          <Modal
            visible={form.visible}
            title={form.mtitle}
            width="620px"
            confirmLoading={this.state.loading}
            onOk={this.handleSubmitAdd}
            key={form.key}
            onCancel={() => this.setState({ editRecord: { ...form, visible: false } })}
          >
            <Form {...formLayout} onSubmit={this.handleSubmitAdd}>
              <Form.Item label="启动页类型">
                {getFieldDecorator('view_type', {
                  initialValue: form.view_type,
                })(
                  <Radio.Group>
                    <Radio value={0}>图片</Radio>
                    <Radio value={1}>视频</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
              <Form.Item label="底部Logo">
                {getFieldDecorator('bottom_logo', {
                  initialValue: form.bottom_logo,
                  rules: [
                    {
                      required: true,
                    },
                  ],
                })(
                  <Radio.Group>
                    <Radio value={0}>不显示</Radio>
                    <Radio value={1}>显示</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
              {values.view_type === 0 && (
                <Form.Item label="启动页图片" extra={<span>仅支持jpg、png、gif图片格式</span>}>
                  {getFieldDecorator('pic_url', {
                    initialValue: form.pic_url,
                    rules: [
                      {
                        required: true,
                        message: '请上传图片',
                      },
                    ],
                    preserve: true,
                  })(
                    <ImageUploader
                      imgsize={1024}
                      isCutting={true}
                      accept={['image/jpeg', 'image/png', 'image/jpg', 'image/gif']}
                    />
                  )}
                  <a
                    href={
                      this.props.form.getFieldValue('bottom_logo') == 0
                        ? '/assets/StartPageTemplate0.png'
                        : '/assets/StartPageTemplate1.png'
                    }
                    download="Template.png"
                  >
                    制作模板下载
                  </a>
                </Form.Item>
              )}

              {values.view_type === 1 && (
                <Form.Item
                  label="启动页视频"
                  extra={
                    <span>
                      仅支持.mp4,.flv,.avi等视频格式
                      <br />
                      请上传
                      {this.props.form.getFieldValue('bottom_logo') == 0 ? '9:19.5' : '9:17.3'}
                      的竖视频，大小不要超过5M
                    </span>
                  }
                >
                  {getFieldDecorator('video_url', {
                    initialValue: form.video_url,
                    rules: [
                      {
                        required: true,
                        message: '请上传视频',
                      },
                    ],
                    preserve: true,
                  })(<VideoUploader size={5} />)}
                </Form.Item>
              )}
              <Form.Item label="计划上架时间">
                {getFieldDecorator('delay_time', {
                  initialValue: form.delay_time,
                  rules: [
                    {
                      required: form.requireTime,
                      message: '请选择计划上架时间',
                    },
                  ],
                })(
                  <DatePicker
                    dropdownClassName="start-page-datepicker"
                    renderExtraFooter={() => (
                      <a
                        onClick={() =>
                          this.props.form.setFieldsValue({ delay_time: moment(Date.now() + 60000) })
                        }
                      >
                        此刻
                      </a>
                    )}
                    showToday={false}
                    showTime={true}
                    format="YYYY-MM-DD HH:mm:ss"
                    disabled={!form.requireTime}
                  />
                )}
              </Form.Item>
              <Form.Item label="计划下架时间">
                {getFieldDecorator('off_time', {
                  initialValue: form.off_time,
                })(
                  <DatePicker
                    showToday={false}
                    showTime={true}
                    format="YYYY-MM-DD HH:mm:ss"
                    disabledDate={diabledOffTime}
                    disabled={!form.requireTime}
                  />
                )}
              </Form.Item>
              <Form.Item label="关联稿件" extra="若留空，则为不关联稿件地址">
                {getFieldDecorator('url', {
                  initialValue: form.url,
                  rules: [
                    {
                      validator: this.validateUrl,
                    },
                  ],
                })(<Input placeholder="填写链接或留空" />)}
              </Form.Item>
              <Form.Item label="启动页主题">
                {getFieldDecorator('title', {
                  initialValue: form.title,
                  rules: [
                    {
                      required: true,
                      message: '请填写启动页主题',
                    },
                    {
                      max: 24,
                      message: '启动页主题不能超过24个字',
                    },
                  ],
                })(<Input placeholder="请填写启动页主题" />)}
              </Form.Item>
            </Form>
          </Modal>

          <Modal
            visible={this.state.video.visible}
            destroyOnClose
            onCancel={() => this.setState({ video: { visible: false, url: '' } })}
            onOk={() => this.setState({ video: { visible: false, url: '' } })}
            closable={false}
          >
            <video
              style={{
                width: '100%',
                height: 'auto',
              }}
              src={this.state.video.url}
              controls
            ></video>
          </Modal>
        </div>
      </>
    );
  }
}

export default StartPageManager;
