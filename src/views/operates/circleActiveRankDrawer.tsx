import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Dropdown,
  Form,
  Icon,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { communityApi, opApi } from '@app/api';
import '@components/business/styles/business.scss';
import { setMLoading } from '@app/utils/utils';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import { Drawer, ImageUploader, VideoUploader } from '@app/components/common';
import ImgUploadList from '@app/components/common/imgUploadList';
import { PermButton } from '@app/components/permItems';
import moment from 'moment';
import { initial } from 'lodash';
import ReactWEditor from 'wangeditor-for-react/lib/core';

const CircleActiveRankDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();
  const descElRef = React.createRef<ReactWEditor>();

  const { title, pic_url, url } = props.record || {};

  const { getFieldDecorator, getFieldValue } = props.form;

  const [portraitList, setPortraitList] = useState<any[]>([]);
  const doSubmit = () => {
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        const body: any = {
          ...values,
          circle_id: props.circle_id,
          sign_in_enable: values.sign_in_enable ? 1 : 0,
        };
        dispatch(setConfig({ mLoading: true }));
        communityApi
          .circleActiveConfigSave(body)
          .then(() => {
            dispatch(setConfig({ mLoading: false }));
            message.success('操作成功');
            props.onEnd(values.type);
          })
          .catch(() => {
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
      }
    });
  };

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  const getPortraitList = () => {
    communityApi.getCircleActivePortraitList({ size: 999 }).then((res: any) => {
      setPortraitList(res.data?.list?.records || []);
    });
  };

  useEffect(() => {
    if (props.visible) {
      getPortraitList();
    }
  }, [props.visible]);

  return (
    <Drawer
      title={'圈友活跃功能配置'}
      visible={props.visible}
      skey={props.key}
      onClose={props.onClose}
      onOk={doSubmit}
      okText="确定"
    >
      <Form {...formLayout}>
        <Form.Item
          label="功能开启"
          extra="包括圈内签到及圈友活跃月度排名功能；关闭后前台不显示相关功能入口，过往数据保留在后台"
        >
          {getFieldDecorator('sign_in_enable', {
            initialValue: props.record?.sign_in_enable ?? false,
            valuePropName: 'checked',
            rules: [
              {
                required: true,
                message: '请选择是否开启',
              },
            ],
          })(<Switch />)}
        </Form.Item>

        <Form.Item label="奖励说明">
          {getFieldDecorator('reward_desc', {
            initialValue: props.record?.reward_desc,
            valuePropName: 'defaultValue',
            rules: [
              {
                required: getFieldValue('sign_in_enable'),
                message: '请输入奖励说明',
              },
              {
                max: 60,
                message: '奖励说明最多60字',
              },
            ],
          })(
            <ReactWEditor
              ref={descElRef}
              className="ugc_topic_form_weditor"
              placeholder="简单介绍对月度活跃圈友的奖励，如头像挂件、现金奖励等"
              // defaultValue={this.state.description}
              // onChange={(description) => {
              //   this.setState({ description });
              //   this.validateDesc();
              // }}
              instanceHook={{
                // 使用方法是，通常 key 代表的钩子是一个对象，可以利用方法来绑定。方法的形参第一位是当前实例的 editor，后面依次是 key 分割代表的对象。
                'config.menus': function (editor, config: any, menus) {
                  config.height = 100;
                  config.menus = ['link'];
                  config.showFullScreen = false;
                  config.linkCheck = function (text: string, link: string) {
                    if (text === link) {
                      message.error('请输入链接文字');
                      return;
                    }
                    return true;
                  };
                },
              }}
            />
          )}
        </Form.Item>

        <Form.Item label="头像挂件">
          {getFieldDecorator('portrait_id', {
            initialValue: props.record?.portrait_id,
            rules: [
              {
                required: getFieldValue('sign_in_enable'),
                message: '请选择头像挂件',
              },
            ],
          })(
            <Select placeholder="请选择头像挂件" allowClear>
              {portraitList.map((item: any) => (
                <Select.Option key={`${item.id}`} value={`${item.id}`}>
                  {item.title}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'CircleActiveRankDrawer' })(
  forwardRef<any, any>(CircleActiveRankDrawer)
);
