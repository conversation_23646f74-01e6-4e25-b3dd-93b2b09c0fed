/* eslint-disable import/prefer-default-export */
import { RequestBody, RequestInfo } from '@app/types';
import fetch from '@app/utils/fetch';
import dataBoard from '@app/views/data/board';

export const communityApi = {
  // body: {id: xxxx, community_pushed: 0|1 0-设置推荐，1-取消推荐，默认为0}
  updateCommunityPushed: (body: RequestBody) =>
    fetch.post<{}>('ugc_article/update_community_pushed', body),
  //用户内容推荐管理/轮播图管理列表
  recommendBannerList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('type_recommend/list', body, request),
  //用户内容推荐管理/轮播图管理新增
  recommendBannerCreate: (body: RequestBody) => fetch.post<{}>('type_recommend/create', body),
  //用户内容推荐管理/轮播图管理编辑
  recommendBannerUpdate: (body: RequestBody) => fetch.post<{}>('type_recommend/update', body),
  //用户内容推荐管理/轮播图管理删除
  recommendBannerDeleted: (body: RequestBody) => fetch.post<{}>('type_recommend/delete', body),
  //用户内容推荐管理/轮播图管理删除
  recommendBannerUpdateStatus: (body: RequestBody) =>
    fetch.post<{}>('type_recommend/update_status', body),
  //用户内容推荐管理列表固定
  recommendBannerListFiexdPosition: (body: RequestBody) =>
    fetch.post<{}>('ugc_article/move_position', body),
  //用户内容推荐管理列表排序
  recommendBannerListMovePosition: (body: RequestBody) =>
    fetch.post<{}>('ugc_article/exchange_order', body),
  //用户内容推荐管理列表排序
  recommendBannerListCancelFixed: (body: RequestBody) =>
    fetch.post<{}>('ugc_article/cancel_fixed', body),
  releaseChangeVisible: (body: RequestBody) => fetch.post<{}>('ugc_article/set_visible', body),
  //用户内容推荐管理/轮播排序
  recommendBannerSort: (body: RequestBody) => fetch.post<{}>('type_recommend/update_sort', body),
  //用户内容推荐管理/添加用户推荐
  recommendFollowCreate: (body: RequestBody) => fetch.post<{}>('new_follow_recommend/create', body),
  //用户内容推荐管理/用户推荐列表删除
  recommendFollowDelete: (body: RequestBody) => fetch.post<{}>('new_follow_recommend/delete', body),
  //用户内容推荐管理/用户推荐列表上下架
  recommendFollowUpdateStatus: (body: RequestBody) =>
    fetch.post<{}>('new_follow_recommend/update_status', body),
  //用户内容推荐管理/用户推荐列表编辑
  recommendFollowEdit: (body: RequestBody) => fetch.post<{}>('new_follow_recommend/update', body),
  //用户内容推荐管理/编辑用户推荐列表详情
  recommendFollowDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('new_follow_recommend/detail', body, request),
  //用户内容推荐管理/推荐用户下拉列表-----new
  recommendAccountSearch: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account/search', body, request),
  // 用户内容推荐管理/推荐用户下拉列表-----new
  recommendAccount_Search: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('account_search/account_select', body, request),
  //用户内容推荐管理/推荐用户列表
  recommendFollowList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('new_follow_recommend/list', body, request),
  //用户内容推荐管理/话题列表
  recommendTopicLabelList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_recommend/list', body, request),
  //用户内容推荐管理/话题列表----new
  recommendTopicLabelListByPage: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_recommend/list_by_page', body, request),
  //用户内容推荐管理/话题添加
  recommendTopicCreate: (body: RequestBody) => fetch.post<{}>('topic_recommend/create', body),
  //用户内容推荐管理/话题编辑
  recommendTopicUpdate: (body: RequestBody) => fetch.post<{}>('topic_recommend/update', body),
  //用户内容推荐管理/话题上下架
  recommendTopicUpdateStatus: (body: RequestBody) =>
    fetch.post<{}>('topic_recommend/update_status', body),
  //用户内容推荐管理/话题删除列表
  recommendTopicListDelete: (body: RequestBody) => fetch.post<{}>('topic_recommend/delete', body),
  //用户内容推荐管理/话题列表
  recommendTopicSearchList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc_topic_recommend/search_name', body, request),
  //用户内容推荐管理/话题列表详情
  recommendTopicListDetail: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_recommend/get_recommend_detail', body, request),

  // 圈子列表
  getCircleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/list', body, request),
  // 圈子创建/编辑
  editCircle: (body: RequestBody) => fetch.post('circle/edit', body),
  // 圈子上下线
  updateCircleStatus: (body: RequestBody) => fetch.post('circle/update_status', body),
  // 圈子删除
  deleteCircle: (body: RequestBody) => fetch.post('circle/delete', body),
  // 圈子排序
  sortCircle: (body: RequestBody) => fetch.post('circle/order', body),
  // 圈子内容列表
  getCircleArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/article/list', body, request),
  // 导出圈子内容列表
  exportCircleArticleList: (body: RequestBody, request?: RequestInfo) =>
    fetch.blob('circle/article/list', body, request),
  // 圈子图片推荐位列表
  getCircleRecommendList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/recommend/list', body, request),
  createCirclePicRecommend: (body: RequestBody) => fetch.post('circle/recommend/create', body),
  editCirclePicRecommend: (body: RequestBody) => fetch.post('circle/recommend/edit', body),
  setCirclePicRecommendStyle: (body: RequestBody) =>
    fetch.get('circle/recommend/set_list_style', body),
  getCirclePicRecommendStyle: (body: RequestBody) => fetch.get('circle/recommend/list_style', body),
  // 圈子排序
  sortCirclePicRecommend: (body: RequestBody) => fetch.post('circle/recommend/update_sort', body),
  deleteCirclePicRecommend: (body: RequestBody) => fetch.post('circle/recommend/delete', body),
  updateCirclePicRecommendStatus: (body: RequestBody) =>
    fetch.post('circle/recommend/update_status', body),

  // 圈子主理人列表
  getCircleOwnerList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/manager/list', body, request),
  // 圈子主理人排序
  sortCircleOwner: (body: RequestBody) => fetch.post('circle/manager/order', body),
  // 删除圈子主理人
  deleteCircleOwner: (body: RequestBody) => fetch.post('circle/manager/delete', body),
  // 创建、编辑圈子主理人
  editCircleOwner: (body: RequestBody) => fetch.post('circle/manager/edit', body),

  // 账号搜索
  searchAdminUser: (body: RequestBody) => fetch.get('admin_user/search', body),
  // 账号权限列表
  getUGCPermissionList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('ugc/circle/permission/list', body, request),
  // 圈子主理人权限列表
  getCircleOwnerPermissionList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/manager/permission/list', body, request),
  // 账号圈子权限详情
  getUGCPermissionDetail: (body: RequestBody) => fetch.get('ugc/circle/permission/detail', body),
  // 账号权限编辑（修改&删除）
  editUGCPermission: (body: RequestBody) => fetch.post('ugc/circle/permission/edit', body),
  // 账号权限编辑（新增)
  addUGCPermission: (body: RequestBody) => fetch.post('ugc/circle/permission/add', body),
  // 版块列表
  getCircleBoardList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/board/list', body, request),
  // 版块排序
  sortCircleBoard: (body: RequestBody) => fetch.post('circle/board/order', body),
  // 版块创建/编辑
  editCircleBoard: (body: RequestBody) => fetch.post('circle/board/edit', body),
  // 版块删除
  deleteCircleBoard: (body: RequestBody) => fetch.post('circle/board/delete', body),
  // 设置内容等级
  setCircleContentLevel: (body: RequestBody) => fetch.post('ugc_article/set_content_level', body),
  // 设置是否精选
  setCircleContentSelected: (body: RequestBody) => fetch.post('circle/article/set_selected', body),
  // 圈子置顶内容查看
  getCircleTopDetail: (body: RequestBody) => fetch.get('circle/article_top/detail', body),
  // 圈子置顶内容编辑
  editCircleTopContent: (body: RequestBody) => fetch.post('circle/article_top/edit', body),
  // 圈子固定内容查看
  getCircleFixList: (body: RequestBody) => fetch.get('circle/article_fix/list', body),
  // 圈子固定内容编辑
  editCircleFixList: (body: RequestBody) => fetch.post('circle/article_fix/edit', body),
  // 调整圈子和版块
  updateCircleBlock: (body: RequestBody) => fetch.post('circle/article/update_circle', body),

  // 手动抓取数据
  spiderPullData: (body: RequestBody) => fetch.post('spider_bind/spider_by_ids', body),
  // 置顶操作记录
  operationRecord: (body: RequestBody) => fetch.get('admin_log/type_list_desc', body),

  // 栏目分组列表
  getColumnGroupsList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('topic_label_column/group_list', body, request),
  // 版块创建/编辑
  addColumnGroup: (body: RequestBody) => fetch.post('topic_label_column/group_create', body),
  // 版块创建/编辑
  editColumnGroup: (body: RequestBody) => fetch.post('topic_label_column/group_edit', body),
  // 分组删除
  deleteColumnGroup: (body: RequestBody) => fetch.post('topic_label_column/group_delete', body),
  // 分组删除
  changeColumnGroup: (body: RequestBody) => fetch.post('topic_label_column/topic_update_group', body),
  sortColumnGroup: (body: RequestBody) => fetch.post('topic_label_column/group_order', body),

  // 下拉获取20条已启用服务列表（支持服务名搜索）
  operationWeblinkOnlist: (body: RequestBody) => fetch.get('web_link/on_list', body),
  // 获取稿件相关服务接口
  channelQrticleRelatedService: (body: RequestBody) => fetch.get('channel_article/related_services', body),
  // 获取数据token接口
  getDataBoardToken: (body: RequestBody) => fetch.get('report/data_board/get_token', body),

  // 圈子活跃用户列表
  getCircleActiveRankList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('circle/active/rank', body, request),
  // 圈子活跃用户列表隐藏
  circleActiveSetHidden: (body: RequestBody) => fetch.json('circle/active/is_hidden', body),
  // 圈子活跃用户列表配置详情
  circleActiveConfigDetail: (body: RequestBody) => fetch.get('circle/active/config_detail', body),
  // 圈子活跃用户列表配置保存
  circleActiveConfigSave: (body: RequestBody) => fetch.json('circle/active/config_save', body),
  // 圈子活跃用户列表活跃值明细
  circleActiveScoreDetail: (body: RequestBody) => fetch.get('circle/active/score_detail', body),

  getCircleActivePortraitList: (body: RequestBody) => fetch.get('circle/active/tx_list', body),

  // 发放奖励
  giveCircleActiveReward: (body: RequestBody) => fetch.post('creator/earnings/give', body),

  // 获取提现金额统计
  getEarningsTotal: (body: RequestBody) => fetch.get('earnings_account/withdrawable_total', body),

  // 获取账户列表
  getEarningsAccountList: (body: RequestBody) => fetch.get('earnings_account/list', body),

  // 导出账户列表
  exportEarningsAccountList: (body: RequestBody) => fetch.blob('earnings_account/export', body),

  // 获取账户明细
  getEarningsFundFlowList: (body: RequestBody, request?: RequestInfo) =>
    fetch.get('earnings_fund_flow/list', body, request),

  // 导出收支记录
  exportEarningsFundFlow: (body: RequestBody) => fetch.blob('earnings_fund_flow/export_fund_flow', body),

  // 导出指定账户的收支记录
  exportEarningsFundFlowByAccountId: (body: RequestBody) => fetch.blob('earnings_fund_flow/export_fund_flow_by_account_id', body),
  

  // 活跃圈友榜单上榜奖励列表接口
  getCircleActiveEarningsList: (body: RequestBody) => fetch.get('earnings_manuscript_income/circle_active/earnings_list', body),
  // 活跃圈友榜单发放奖励接口
  giveCircleActiveEarnings: (body: RequestBody) => fetch.json('earnings_manuscript_income/circle_active/earnings_give', body),
  // 查询稿件创作收益明细接口
  getEarningsArticleDetail: (body: RequestBody) => fetch.get('earnings_manuscript_income/article_earnings_detail', body),
  // 查询稿件上榜详情接口
  getEarningsArticleRankDetail: (body: RequestBody) => fetch.get('earnings_manuscript_income/rank/detail', body),
};
